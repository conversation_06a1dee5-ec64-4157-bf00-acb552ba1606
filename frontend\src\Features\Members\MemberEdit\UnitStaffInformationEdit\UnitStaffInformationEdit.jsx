import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import {
  updateUnitStaffStatus,
  clearMessage,
  setShowMessage
} from "../../../../redux/slices/unitStaff/unitStaffSlice";
import { setActiveTabs } from "../../../../redux/slices/memberSlice";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import { BiArrowBack } from "react-icons/bi";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

const UnitStaffInformationEdit = () => {
  const { staffid, staffstatus } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // pull these from your slice
  const { message, error, showMessage } = useSelector(
    (state) => state.unitStaff
  );

  if (!staffid) {
    console.error("staffid is undefined. Check your route.");
    return (
      <div className="p-5 text-center">
        <LoadingAnimation/>
      </div>
    );
  }

  const defaultStatus = staffstatus === "true" ? "Live-in" : "Part-time";

  const { register, handleSubmit, watch, reset } = useForm({
    defaultValues: { staffStatus: defaultStatus }
  });

  const selectedStatus = watch("staffStatus");

  useEffect(() => {
    reset({ staffStatus: defaultStatus });
  }, [staffstatus, reset]);

  const onSubmit = async (data) => {
    const nextStatus = data.staffStatus === "Live-in";
    const result = await dispatch(
      updateUnitStaffStatus({ id: staffid, unit_staff_status: nextStatus })
    );

    if (updateUnitStaffStatus.fulfilled.match(result)) {
      // show success
      dispatch(setShowMessage(true));
    } else {
      // show error
      dispatch(setShowMessage(true));
    }
  };

  return (
    <div className="bg-white border p-5 rounded-xl shadow-sm lg:w-[787px] m-2">
      <button
        type="button"
        onClick={() => {
          dispatch(setActiveTabs(3));
          navigate(-1);
        }}
        className="inline-flex items-center cursor-pointer text-xl mb-2 "
      >
        <BiArrowBack className={"mr-2 text-primary"} />
        Back
      </button>
      {showMessage && (
        <MessageBox
          message={message}
          error={error}
          clearMessage={() => {
            dispatch(clearMessage());
            dispatch(setShowMessage(false));
          }}
          onOk={() => {
            dispatch(clearMessage());
            dispatch(setShowMessage(false));
            if (!error) {
              dispatch(setActiveTabs(3));
              navigate(-1);
            }
          }}
        />
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="max-w-md p-6">
        <div className="space-y-2 mb-6">
          {["Live-in", "Part-time"].map((status) => (
            <label
              key={status}
              className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                selectedStatus === status
                  ? "border-primary "
                  : "border-gray-300 "
              }`}
            >
              <input
                type="radio"
                value={status}
                {...register("staffStatus", { required: true })}
                className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-primary accent-primary"
              />
              <div className="ml-3">
                <span className="block text-sm font-medium text-primary">
                  {status}
                </span>
                {/* <span className="block text-sm text-primary">
                  {status === "Live-in" ? "Full-time staff" : "Scheduled staff"}
                </span> */}
              </div>
            </label>
          ))}
        </div>

        <button
          type="submit"
          disabled={selectedStatus === defaultStatus}
          className={`w-full py-2 px-4 rounded-lg ${
            selectedStatus === defaultStatus
              ? "bg-gray-300 text-gray-600 cursor-not-allowed"
              : "bg-primary text-white hover:bg-primary-dark"
          }`}
        >
          Update
        </button>
      </form>
    </div>
  );
};

export default UnitStaffInformationEdit;
