// src/Components/Modals/AddRoleModal.jsx
import React from "react";
import AddMemberRole from "../../Features/Members/OrganizationMemberForm/AddMemberRole";

const AddRoleModal = ({ isOpen, onClose, onRoleCreated }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      {/* Modal backdrop */}
      <div
        className="absolute inset-0 bg-gray-900 opacity-50"
        onClick={onClose}
      ></div>
      {/* Modal content */}
      <div className="relative bg-white px-6 rounded shadow-lg z-10 ">
        <div className="flex justify-end">
          <button
            className="text-gray-600 text-xl font-bold"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <AddMemberRole onRoleCreated={onRoleCreated}  onClose={onClose}/>
        {/* <div className="flex justify-end ">
          <button
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded"
            onClick={onClose}
          >
            Cancel
          </button>
        </div> */}
      </div>
    </div>
  );
};

export default AddRoleModal;
