import React, { useEffect, useState } from "react";
import { FiX } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { fetchAddExistingMembers } from "../../../redux/slices/api/memberApi";
import { Div } from "Components/Ui/Div";
import SearchBar from "Components/Search/SearchBar";
import Heading from "Components/HeadingComponent/Heading";
import FilterSelect2 from "../../../Components/FilterSelect/FilterSelect2";
import { useSearchParams } from "react-router-dom";
import { fetchMemberTypeOptions } from "./memberTypeList";

const AddExistingCommMemberTable = ({ isOpen, onClose, onSelect }) => {
  if (!isOpen) return null;

  
  const BASE_URL = import.meta.env.VITE_BASE_API;
  const dispatch = useDispatch();

  const allMemberData =
    useSelector((state) => state.member?.AddExistingCommMember) || {};

  const [selectUnit, setSelectUnit] = useState([]);
  const [filters, setFilters] = useState({ member_type: [], search: "" });
  const [searchParams, setSearchParams] = useSearchParams();
  const allMembers = [
    ...(allMemberData.owners || []),
    ...(allMemberData.comm_members || []),
    ...(allMemberData.resident_members || []),
    ...(allMemberData.unit_staff|| []),
  ];

  // Fetch filter options on mount
 useEffect(() => {
   
    (async () => {
      const options = await fetchMemberTypeOptions();
      setSelectUnit(options);
    })();
  }, []);

  // Extract and parse filters from URL
  useEffect(() => {
  const roleFilter = searchParams.get("member_type");
  const searchQuery = searchParams.get("search");

  const roleFilterArray = roleFilter
    ? roleFilter.split(",").map((item) =>
        /^\d+$/.test(item) ? Number(item) : item
      )
    : [];

  const updatedFilters = {
    member_type: roleFilterArray,
    search: searchQuery || "",
  };

  setFilters(updatedFilters);

  // ✅ Dispatch directly inside the same effect
  dispatch(fetchAddExistingMembers({ filters: updatedFilters }));
}, [searchParams, dispatch]);


  const getMemberValue = (member, key) => {

   console.log('get:', member?.[key]); 

    if (key === "member_type_name") {
      if (member?.resident_member?.[key]) return "Resident";
      if ( (member?.member?.[key])&& !member?.unit_staff) return "Owner";
      if ((member?.member?.[key]) && member?.unit_staff) return "Unit Staff";
      return  member?.[key] || "-";
    }

    return (
     
      member?.resident_member?.[key] ||
      member?.member?.[key] ||
      member?.[key] ||
      "-"
    );
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative bg-white rounded-xl p-6 w-full md:max-w-[1000px] mx-4">
        {/* Close Button */}
        <button
         onClick={() => {
          onClose();
          setSearchParams({});
        }}
        
          className="absolute top-2 right-2 text-white bg-primary p-1 rounded-md"
        >
          <FiX size={20} />
        </button>

        <Div className="flex justify-between items-center py-4">
          <Heading
            title="Community Members List"
            size="lg"
            color="text-black"
          />
          <Div className="flex items-center space-x-4 px-5">
            <FilterSelect2
              placeholder="Select Type"
              options={selectUnit}
              paramKey="member_type"
              onApply={(filters) => {
                if (!filters) {
                  console.error("Filters object is undefined");
                  return;
                }
                console.log("Applied Filters:", filters);
              }}
            />
            <SearchBar />
          </Div>
        </Div>

        <div className="overflow-x-auto overflow-y-auto max-h-[500px]">
          {allMembers.length > 0 ? (
            <table className="min-w-full text-sm text-left border border-gray-200">
              <thead className="bg-teal-50">
                <tr>
                  {[
                    "Name",
                    "Contact",
                    "Type",
                    "Email",
                    "Occupation",
                    "Tower",
                    "Floor",
                    "Unit",
                    "Action",
                  ].map((title, i) => (
                    <th
                      key={i}
                      className="px-4 py-3 font-semibold text-gray-800 border-b"
                    >
                      {title}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {allMembers.map((member, index) => (
                  <tr
                    key={index}
                    className="bg-white border-b hover:bg-gray-50"
                  >
                    <td className="px-4 py-3 flex items-center gap-2">
                      <img
                        src={
                          member?.photo ||
                          member?.resident_member?.photo ||
                          member?.member?.photo
                            ? `${BASE_URL}${
                                member?.photo ||
                                member?.resident_member?.photo ||
                                member?.member?.photo
                              }`
                            : "/user.jpg"
                        }
                        onError={(e) => (e.target.src = "/user.jpg")}
                        alt={getMemberValue(member, "full_name")}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      {getMemberValue(member, "full_name")}
                    </td>
                    <td className="px-4 py-3">
                      {getMemberValue(member, "general_contact")}
                    </td>
                    <td className="px-4 py-3 capitalize">
                      {getMemberValue(member, "member_type_name")}
                    </td>
                    <td className="px-4 py-3">
                      {getMemberValue(member, "general_email")}
                    </td>
                    <td className="px-4 py-3">
                      {getMemberValue(member, "occupation")}
                    </td>
                    <td className="px-4 py-3">
                      {getMemberValue(member, "tower_name")}
                    </td>
                    <td className="px-4 py-3">
                      {getMemberValue(member, "floor_no")}
                    </td>
                    <td className="px-4 py-3 capitalize">
                      {getMemberValue(member, "unit_name")}
                    </td>
                    <td className="px-4 py-3 text-center">
                      <button
                        onClick={() => onSelect(member)}
                        className="bg-teal-600 hover:bg-teal-700 text-white px-3 py-1 rounded text-sm"
                      >
                        Add
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No members available
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddExistingCommMemberTable;
