from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ars<PERSON>
from django.db.models import Q
from django.utils import timezone
from django.core.files.base import ContentFile
import base64
import uuid
from .models import Announcement, AnnouncementAttachment
from .serializers import (
    AnnouncementSerializer,
    AnnouncementListSerializer,
    AnnouncementAttachmentSerializer
)
from towers.models import Tower, Unit


class AnnouncementViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing announcements with file upload support
    """
    permission_classes = [IsAuthenticated]
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONParser]

    def get_queryset(self):
        queryset = Announcement.objects.all()

        # Filter by status
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Search functionality
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search) |
                Q(creator__full_name__icontains=search) |
                Q(posted_group__name__icontains=search) |
                Q(posted_member__full_name__icontains=search)
            )

        # Filter by priority
        priority = self.request.query_params.get('priority', None)
        if priority:
            queryset = queryset.filter(priority=priority)

        # Filter by label
        label = self.request.query_params.get('label', None)
        if label:
            queryset = queryset.filter(label=label)

        # Filter by my posts (current user's announcements)
        my_posts = self.request.query_params.get('my_posts', None)
        if my_posts and my_posts.lower() == 'true':
            queryset = queryset.filter(
                Q(creator=self.request.user.member) |
                Q(posted_member=self.request.user.member) |
                Q(posted_group__members=self.request.user.member)
            ).distinct()

        # Order by pinned first, then by creation date
        queryset = queryset.order_by('-is_pinned', '-created_at')

        return queryset.select_related(
            'creator', 'posted_group', 'posted_member'
        ).prefetch_related(
            'attachments', 'target_towers', 'target_units', 'history'
        )

    def get_serializer_class(self):
        # Temporarily use full serializer for list to test attachments
        # if self.action == 'list':
        #     return AnnouncementListSerializer
        return AnnouncementSerializer

    def create(self, request, *args, **kwargs):
        """
        Create announcement with file attachments using FormData or JSON with base64
        """
        # Extract files from request
        files = request.FILES.getlist('attachments')

        # Handle base64 attachments from JSON request
        base64_attachments = request.data.get('base64_attachments', [])

        # Create announcement data
        data = request.data.copy()

        # Handle post_as specific fields
        post_as = data.get('post_as')
        if post_as == 'group':
            group_id = data.get('posted_group')
            if not group_id:
                return Response({
                    'error': 'Validation failed',
                    'details': {'posted_group': 'Group is required when posting as a group'}
                }, status=status.HTTP_400_BAD_REQUEST)
            data['posted_group'] = group_id
            data['posted_member'] = None
        elif post_as == 'member':
            member_id = data.get('posted_member')
            if not member_id:
                return Response({
                    'error': 'Validation failed',
                    'details': {'posted_member': 'Member is required when posting as a member'}
                }, status=status.HTTP_400_BAD_REQUEST)
            data['posted_member'] = member_id
            data['posted_group'] = None
        else:  # creator
            data['posted_group'] = None
            data['posted_member'] = None

        # Handle tower and unit IDs based on content type
        if 'target_tower_ids' in data:
            tower_ids = data.get('target_tower_ids')
            if isinstance(tower_ids, str):
                # FormData - convert from comma-separated string
                data['target_tower_ids'] = [int(id.strip()) for id in tower_ids.split(',') if id.strip()]
            elif isinstance(tower_ids, list):
                # JSON - already a list, ensure integers
                data['target_tower_ids'] = [int(id) for id in tower_ids if str(id).strip()]

        if 'target_unit_ids' in data:
            unit_ids = data.get('target_unit_ids')
            if isinstance(unit_ids, str):
                # FormData - convert from comma-separated string
                data['target_unit_ids'] = [int(id.strip()) for id in unit_ids.split(',') if id.strip()]
            elif isinstance(unit_ids, list):
                # JSON - already a list, ensure integers
                data['target_unit_ids'] = [int(id) for id in unit_ids if str(id).strip()]

        # Create announcement
        serializer = self.get_serializer(data=data)
        if not serializer.is_valid():
            # Return detailed validation errors
            return Response({
                'error': 'Validation failed',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        announcement = serializer.save(creator=request.user.member)

        # Handle regular file attachments
        for file in files:
            # Validate file type and size
            if not self._is_valid_file(file):
                continue

            AnnouncementAttachment.objects.create(
                announcement=announcement,
                file=file,
                file_name=file.name,
                file_type=file.content_type,
                file_size=file.size
            )

        # Handle base64 attachments
        for attachment_data in base64_attachments:
            try:
                self._create_attachment_from_base64(announcement, attachment_data)
            except Exception as e:
                print(f"Error processing base64 attachment: {e}")
                continue

        # Return response with created announcement
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        """
        Update announcement with file attachments using FormData or JSON
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Extract files from request
        files = request.FILES.getlist('attachments')

        # Handle base64 attachments from JSON request
        base64_attachments = request.data.get('base64_attachments', [])

        # Handle attachment deletions
        attachments_to_delete = request.data.get('attachments_to_delete', [])

        # Create announcement data
        data = request.data.copy()

        # Handle tower and unit IDs based on content type
        if 'target_tower_ids' in data:
            tower_ids = data.get('target_tower_ids')
            if isinstance(tower_ids, str):
                # FormData - convert from comma-separated string
                data['target_tower_ids'] = [int(id.strip()) for id in tower_ids.split(',') if id.strip()]
            elif isinstance(tower_ids, list):
                # JSON - already a list, ensure integers
                data['target_tower_ids'] = [int(id) for id in tower_ids if str(id).strip()]

        if 'target_unit_ids' in data:
            unit_ids = data.get('target_unit_ids')
            if isinstance(unit_ids, str):
                # FormData - convert from comma-separated string
                data['target_unit_ids'] = [int(id.strip()) for id in unit_ids.split(',') if id.strip()]
            elif isinstance(unit_ids, list):
                # JSON - already a list, ensure integers
                data['target_unit_ids'] = [int(id) for id in unit_ids if str(id).strip()]

        # Update announcement
        serializer = self.get_serializer(instance, data=data, partial=partial)
        if not serializer.is_valid():
            # Return detailed validation errors
            return Response({
                'error': 'Validation failed',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        announcement = serializer.save()

        # Handle attachment deletions first
        if attachments_to_delete:
            for attachment_id in attachments_to_delete:
                try:
                    attachment = AnnouncementAttachment.objects.get(
                        id=attachment_id,
                        announcement=announcement
                    )
                    # Delete the file from storage
                    if attachment.file:
                        attachment.file.delete(save=False)
                    # Delete the attachment record
                    attachment.delete()
                except AnnouncementAttachment.DoesNotExist:
                    print(f"Attachment {attachment_id} not found or doesn't belong to this announcement")
                except Exception as e:
                    print(f"Error deleting attachment {attachment_id}: {e}")

        # Handle new file attachments (add to existing ones)
        for file in files:
            # Validate file type and size
            if not self._is_valid_file(file):
                continue

            AnnouncementAttachment.objects.create(
                announcement=announcement,
                file=file,
                file_name=file.name,
                file_type=file.content_type,
                file_size=file.size
            )

        # Handle base64 attachments
        for attachment_data in base64_attachments:
            try:
                self._create_attachment_from_base64(announcement, attachment_data)
            except Exception as e:
                print(f"Error processing base64 attachment: {e}")
                continue

        return Response(serializer.data)

    def _is_valid_file(self, file):
        """
        Validate file type and size
        """
        # Check file size (5MB limit)
        if file.size > 5 * 1024 * 1024:
            return False

        # Check file type
        allowed_types = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]

        return file.content_type in allowed_types

    def _create_attachment_from_base64(self, announcement, attachment_data):
        """
        Create attachment from base64 data
        """
        try:
            # Extract base64 data and metadata
            base64_string = attachment_data.get('base64', '')
            file_name = attachment_data.get('name', f'attachment_{uuid.uuid4().hex[:8]}')
            file_type = attachment_data.get('type', 'application/octet-stream')

            # Remove data URL prefix if present
            if ',' in base64_string:
                base64_string = base64_string.split(',')[1]

            # Decode base64
            file_data = base64.b64decode(base64_string)
            file_size = len(file_data)

            # Validate file size (5MB limit)
            if file_size > 5 * 1024 * 1024:
                return False

            # Create Django file object
            django_file = ContentFile(file_data, name=file_name)

            # Create attachment
            AnnouncementAttachment.objects.create(
                announcement=announcement,
                file=django_file,
                file_name=file_name,
                file_type=file_type,
                file_size=file_size
            )

            return True

        except Exception as e:
            print(f"Error creating attachment from base64: {e}")
            return False

    @action(detail=False, methods=['get'])
    def by_status(self, request):
        """
        Get announcements grouped by status
        """
        ongoing = self.get_queryset().filter(status='ongoing')
        upcoming = self.get_queryset().filter(status='upcoming')
        expired = self.get_queryset().filter(status='expired')

        return Response({
            'ongoing': AnnouncementListSerializer(ongoing, many=True).data,
            'upcoming': AnnouncementListSerializer(upcoming, many=True).data,
            'expired': AnnouncementListSerializer(expired, many=True).data,
        })

    @action(detail=True, methods=['post'])
    def toggle_pin(self, request, pk=None):
        """
        Toggle pin status of an announcement
        """
        announcement = self.get_object()
        announcement.is_pinned = not announcement.is_pinned
        announcement.save()

        return Response({
            'message': f'Announcement {"pinned" if announcement.is_pinned else "unpinned"} successfully',
            'is_pinned': announcement.is_pinned
        })

    @action(detail=True, methods=['post'])
    def increment_views(self, request, pk=None):
        """
        Increment view count for an announcement
        """
        announcement = self.get_object()
        announcement.views += 1
        announcement.save(update_fields=['views'])

        return Response({'views': announcement.views})

    @action(detail=True, methods=['post'])
    def force_expire(self, request, pk=None):
        """
        Force an announcement to expired status
        """
        announcement = self.get_object()
        announcement.status = 'expired'
        announcement.manually_expired = True
        announcement.save(update_fields=['status', 'manually_expired'])

        return Response({'message': 'Announcement moved to expired status'})

    @action(detail=True, methods=['post'])
    def restore(self, request, pk=None):
        """
        Restore a manually expired announcement
        """
        announcement = self.get_object()
        announcement.manually_expired = False
        # Recalculate status based on dates
        announcement.update_status()
        announcement.save()

        return Response({
            'message': 'Announcement restored successfully',
            'status': announcement.status
        })

    @action(detail=False, methods=['post'])
    def update_statuses(self, request):
        """
        Update all announcement statuses based on current date/time
        """
        announcements = Announcement.objects.all()
        updated_count = 0

        for announcement in announcements:
            old_status = announcement.status
            announcement.update_status()
            if announcement.status != old_status:
                updated_count += 1

        return Response({
            'message': f'Updated {updated_count} announcement statuses',
            'updated_count': updated_count
        })


class TowerListView(APIView):
    """
    API view to get all towers for announcement targeting
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        towers = Tower.objects.all().values('id', 'tower_name', 'tower_number')
        return Response(list(towers))


class UnitListView(APIView):
    """
    API view to get units, optionally filtered by tower IDs
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        tower_ids = request.query_params.get('tower_ids', None)

        if tower_ids:
            # Parse comma-separated tower IDs
            tower_id_list = [int(id.strip()) for id in tower_ids.split(',') if id.strip().isdigit()]
            units = Unit.objects.filter(floor__tower__id__in=tower_id_list)
        else:
            units = Unit.objects.all()

        # Get units with tower information
        units_data = []
        for unit in units.select_related('floor__tower'):
            units_data.append({
                'id': unit.id,
                'unit_name': unit.unit_name,
                'tower_id': unit.floor.tower.id,
                'tower_name': unit.floor.tower.tower_name,
            })

        return Response(units_data)


class AnnouncementAttachmentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing announcement attachments
    """
    permission_classes = [IsAuthenticated]
    serializer_class = AnnouncementAttachmentSerializer

    def get_queryset(self):
        announcement_id = self.request.query_params.get('announcement_id', None)
        if announcement_id:
            return AnnouncementAttachment.objects.filter(announcement_id=announcement_id)
        return AnnouncementAttachment.objects.all()

    def perform_create(self, serializer):
        # Handle file upload and set file metadata
        file = self.request.FILES.get('file')
        if file:
            serializer.save(
                file_name=file.name,
                file_type=file.content_type,
                file_size=file.size
            )
