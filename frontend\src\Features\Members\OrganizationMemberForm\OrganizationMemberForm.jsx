import { useEffect, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { FaPlus } from "react-icons/fa";

import MemberMainForm from "../../Members/MemberMainForm/MemberMainForm";
import MemberSideForm from "../../Members/MemberSideForm/MemberSideForm";
import { memberFields } from "utils/formFields";
import { Div } from "Components/Ui/Div";
import LoginCredential from "../../Login/LoginCredential/LoginCredential";
import ArrowHeading from "Components/HeadingComponent/ArrowHeading";
import TabButton from "Components/FormComponent/ButtonComponent/TabButton";
import SubmitButton from "Components/FormComponent/ButtonComponent/SubmitButton";
import NavigateButton from "Components/FormComponent/ButtonComponent/NavigateButton";

// Redux API Calls
import { fetchMemberTypes } from "../../../redux/slices/api/memberApi";
import { memberfetchRoles } from "../../../redux/slices/roles/rolesSlice";
import { addExistingContact } from "../../../redux/slices/units/unitSlice";

// Member Role & Type Assignment Components
import MemberRoleAsign from "./../MemberRoleAsign/MemberRoleAsign";
import MemberTypeAsign from "../MemberTypeAsign/MemberTypeAsign";

// Custom Hooks for handling different functionalities
import useMemberValidation from "./useMemberValidation";
import useHandleFileChange from "utils/useHandleFileChange";
import useMemberSelections from "./useMemberSelections";
import useMemberSubmit from "./useMemberSubmit";
import useHandleChange from "utils/useHandleChange";
import MessageBox from "../../../Components/MessageBox/MessageBox";
import LoadingAnimation from "../../../Components/Loaders/LoadingAnimation";

// Permission utility function
import { checkPermission } from "../../../utils/permissionUtils";

// Import ComMemberTable for the existing member modal
import ComMemberTable from "../../../Features/TowersAndUnits/Units/AddUnits/ComMemberTable";
import AddExistingCommMemberTable from "../MemberTable/AddExistingCommMemberTable";
import AddRoleModal from '../AddRoleModal';

// Set your base URL to prepend for file previews
const baseURL = import.meta.env.VITE_BASE_API || "http://127.0.0.1:8000";

const OrganizationMemberForm = () => {
  const [showModal, setShowModal] = useState(false);

  // State for handling active tab number
  const [activeTab, setActiveTab] = useState(1);
  const navigate = useNavigate();

  // State for storing form data. Note: for second tab fields, default values are empty.
  const [formData, setFormData] = useState({
    member_type: "",
    members_role: [],
    is_org_member: 1,
    org_member_ever_created: 1,
    full_name: "",
    general_email: "",
    general_contact: "",
    permanent_address: "",
    present_address: "",
    date_of_birth: "",
    occupation: "",
    gender: "",
    marital_status: "",
    religion: "",
    about_us: "",
    facebook_profile: "",
    linkedin_profile: "",
    nid_number: "",
    photo: "",
    nid_front: "",
    nid_back: "",
    // For image previews if a file is not selected
    previewPhoto: undefined,
    previewNidFront: undefined,
    previewNidBack: undefined,
  });

  // State for permission check
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  // State for handling the Existing Member Modal
  const [showMemberModal, setShowMemberModal] = useState(false);

  // Custom Hook for handling input changes
  const { handleChange } = useHandleChange(setFormData);

  // Redux Hooks for fetching roles and member type data
  const dispatch = useDispatch();
  const { roles } = useSelector((state) => state.role || {});
  const { memberTypes } = useSelector((state) => state.member || {});
  // Get the member contact data from the "unit" slice
  const { memberContact } = useSelector((state) => state.unit || {});
  const [searchParams, setSearchParams] = useSearchParams();
  const [autofillDisabled, setAutofillDisabled] = useState(false);
  const [is_org_member, setIsOrgMember] = useState(false);
  // Custom hook for role/type selections
  const {
    selectedRoleValues,
    setSelectedRoleValues,
    selectedTypeValues,
    setSelectedTypeValues,
    handleRoleSelection,
  } = useMemberSelections(setFormData);
  const [isAddRoleModalOpen, setIsAddRoleModalOpen] = useState(false);

  // Fetch roles and member types on component mount
  useEffect(() => {
    if (!roles.length || roles.some((role) => !role.is_active)) {
      dispatch(memberfetchRoles());
    }
    if (!memberTypes.length) {
      dispatch(fetchMemberTypes());
    }
  }, [dispatch, roles, memberTypes]);

  // Dispatch to load existing member contacts
  useEffect(() => {
    dispatch(addExistingContact());
  }, [dispatch]);

  // Custom Hook for form validation
  const { errors, validateForm, setErrors } = useMemberValidation();

  // Custom Hook for handling file uploads
  const { handleFileChange, handleFile3, nidFront, nidBack } = useHandleFileChange(setFormData);

  // Custom Hook for handling form submission
  const {
    handleSubmit,
    loading,
    showMessage,
    message,
    error,
    clearMessage,
    handleDismissMessage,
  } = useMemberSubmit(formData, validateForm, activeTab);

  // Permission Check Logic
  useEffect(() => {


    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 1);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  if (loadingPermission) {
    return (
      <div className="flex items-center justify-center my-12">
        {/* <LoadingAnimation /> */}
      </div>
    );
  }
  if (!hasPermission) {
    navigate("/not-authorized");
  }

  /**
   * Handle tab switching with optional validation.
   */
  const handleTabChange = (tabNumber) => {
    if (activeTab < tabNumber) {
      const validationPassed = validateForm(formData, activeTab);
      if (!validationPassed) return;
    }
    if (activeTab === 2 && tabNumber === 1) {
      setErrors((prevErrors) => {
        const { memberType, ...restErrors } = prevErrors;
        return restErrors;
      });
    }
    setActiveTab(tabNumber);
  };

  // Callback when a new role is created from the modal in tab 2
  const handleRoleCreated = (newRole) => {
    // Refresh the roles list
    dispatch(memberfetchRoles());
    // Stay on tab 2 and update the selected roles if needed
    setActiveTab(2);
    setSelectedRoleValues((prev) => [...prev, newRole.id]);
    setFormData((prev) => ({
      ...prev,
      members_role: [...prev.members_role, newRole.id],
    }));
    
  };

  /**
   * Callback to handle selection of an existing member.
   * Autofills all form fields (in MemberMainForm, MemberSideForm, and second tab) and sets file preview URLs.
   */
  const handleExistingMemberSelect = (member) => {

    // console.log(member)
      // console.log(member)

    setFormData((prev) => ({
      ...prev,
    
      full_name: member.full_name,
      general_email: member.general_email,
      general_contact: member.general_contact,
      permanent_address: member.permanent_address || "",
      present_address: member.present_address || "",
      date_of_birth: member.date_of_birth || "",
      occupation: member.occupation || "",
      gender: member.gender || "",
      marital_status: member.marital_status || "",
      religion: member.religion || "",
      about_us: member.about_us || "",
      facebook_profile: member.facebook_profile || "",
      linkedin_profile: member.linkedin_profile || "",
      nid_number: member.nid_number || "",
      // For file fields, we set the actual value to blank and use preview keys
      photo: "",
      nid_front: "",
      nid_back: "",
      previewPhoto: member.photo ? `${baseURL}${member.photo}` : undefined,
      previewNidFront: member.nid_front ? `${baseURL}${member.nid_front}` : undefined,
      previewNidBack: member.nid_back ? `${baseURL}${member.nid_back}` : undefined,
      // Also auto-select the member type from the selected member.
      member_type: member.member_type || "",
      // And auto-select member roles if available.
      members_role:
        member.member_roles && member.member_roles.length > 0
          ? member.member_roles.map((mr) =>
            mr.role ? mr.role.id : mr.id
          )
          : [],
    }));



    // Update the custom hook states for role and type selections
    if (member.member_roles && member.member_roles.length > 0) {
      const rolesFromMember = member.member_roles.map((mr) =>
        mr.role ? mr.role.id : mr.id
      );
      setSelectedRoleValues(rolesFromMember);
    }
    if (member.member_type) {
      setSelectedTypeValues(member.member_type);
    }

    setShowMemberModal(false);
    setSearchParams({});
  };



  const handleExistingCommMemberSelect = (member) => {
    console.log(member);
    const fallback = (field) =>
      member?.member?.[field] || member?.resident_member?.[field]|| member?.[field] ||  "";
// console.log(fallback("id"))
      if(fallback("org_member_ever_created")){
       setIsOrgMember('This member is already an Organization Member.')
        return    
      }

      setFormData((prev) => {
        const updatedData = {
          ...prev,
          id:parseInt(fallback("id")),
          full_name: fallback("full_name"),
          general_email: fallback("general_email"),
          general_contact: fallback("general_contact"),
          permanent_address: fallback("permanent_address"),
          present_address: fallback("present_address"),
          date_of_birth: fallback("date_of_birth"),
          occupation: fallback("occupation"),
          gender: fallback("gender"),
          marital_status: fallback("marital_status"),
          religion: fallback("religion"),
          about_us: fallback("about_us"),
          facebook_profile: fallback("facebook_profile"),
          linkedin_profile: fallback("linkedin_profile"),
          nid_number: fallback("nid_number"),
          delivery_method: fallback("login_email") || fallback("login_contact") || null,
          is_first_login:fallback("is_first_login"),

          photo: "",
          nid_front: "",
          nid_back: "",
          previewPhoto:
            member?.photo || member?.member?.photo
              ? `${baseURL}${member?.photo || member?.member?.photo}`
              : undefined,
          previewNidFront:
            member?.nid_front || member?.member?.nid_front
              ? `${baseURL}${member?.nid_front || member?.member?.nid_front}`
              : undefined,
          previewNidBack:
            member?.nid_back || member?.member?.nid_back
              ? `${baseURL}${member?.nid_back || member?.member?.nid_back}`
              : undefined,

          member_type: member?.member_type || member?.member?.member_type || "",
          members_role: (
            member?.member_roles ||
            member?.member?.member_roles ||
            []
          ).map((mr) => (mr?.role ? mr.role.id : mr?.id)),
        };

        const login_email=fallback("login_email")
        const login_contact=fallback("login_contact")
// console.log(login_email,login_contact)
        if (login_email) {
          updatedData.login_email = login_email;
        }

        if (login_contact) {
          updatedData.login_contact =login_contact ;
        }

        return updatedData; 

     
      });


    // Handle role and type selections from either level
    const rolesFromMember =
      member?.member_roles?.length > 0
        ? member.member_roles
        : member?.member?.member_roles || [];

    if (rolesFromMember.length > 0) {
      setSelectedRoleValues(
        rolesFromMember.map((mr) => (mr?.role ? mr.role.id : mr?.id))
      );
    }

    const typeValue = member?.member_type || member?.member?.member_type;
    if (typeValue) {
      setSelectedTypeValues(typeValue);
    }

    setAutofillDisabled(true);
    setShowModal(false);
    setSearchParams({});
  };

  // const handleRoleCreatedWrapper = (newRole) => {
  //   if (onRoleCreated) onRoleCreated(newRole);
  //   setIsAddRoleModalOpen(false);
  // };

  // const handleSelect = (member) => {
  //   setSelectedMember(member);
  //   setShowModal(false);
  //   console.log("Selected:", member);
  // };
  return (
    <Div className="h-full ">
      <Div className="container">
        <Div className="md:flex justify-between py-2 ">
          <Link to="/member-list">
            <ArrowHeading title="Add Member" size="xl" />
          </Link>
          {activeTab === 1 && (
            <NavigateButton
              size="medium"
              icon={FaPlus}
              className="bg-primary text-white"
              onClick={() => setShowModal(true)}

            >
              Add Existing Member
            </NavigateButton>
          )}
        </Div>
        <AddExistingCommMemberTable
          isOpen={showModal}
          onClose={() => setShowModal(false)


          }

          onSelect={handleExistingCommMemberSelect}
        />

        {(showMessage|| is_org_member) && (
          <MessageBox
            message={message}
            error={error ||is_org_member }
            clearMessage={handleDismissMessage}
            onOk={() => {

              dispatch(handleDismissMessage);
              setIsOrgMember(false);

              if (message) navigate("/member-list");
            }}
          />
        )}
        <form onSubmit={handleSubmit} autoComplete="off">
          {loading && (
            <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
              <LoadingAnimation />
            </div>
          )}
          <input type="hidden" name="is_org_member" value={formData.is_org_member} />
          <Div className="flex">
            <fieldset disabled={autofillDisabled}>

              <MemberSideForm
                memberFields={memberFields}
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
                onFileChange={handleFile3}
                allowedTypes={["image/jpeg", "image/png", "image/jpg"]}
                errorMessage="Please upload a valid image"
                savedPhoto={
                  formData.photo instanceof File
                    ? undefined
                    : formData.previewPhoto
                }
              />
            </fieldset>

            <Div className="bg-white border-l p-5 rounded-tr-xl rounded-br-xl shadow-sm lg:w-[787px]">
              {activeTab === 1 && (
                <Div>
                  <fieldset disabled={autofillDisabled}>
                  <MemberMainForm
                    formData={formData}
                    setFormData={setFormData}
                    memberFields={memberFields}
                    errors={errors}
                    handleChange={handleChange}
                    onFileChange={handleFileChange}
                    savedFront={
                      formData.nid_front instanceof File
                        ? nidFront
                        : formData.previewNidFront
                    }
                    savedBack={
                      formData.nid_back instanceof File
                        ? nidBack
                        : formData.previewNidBack
                    }
                  />
                  </fieldset>
                  <TabButton label="Next" tabIndex={2} handleTabChange={handleTabChange} />
                </Div>
              )}
              {activeTab === 2 && (
                <Div>
                  <Div className="md:flex justify-between py-2" onClick={() => handleTabChange(1)}>
                    <ArrowHeading title="Organization Member Information" size="md" />
                  </Div>
                  <MemberTypeAsign
                    label="Member Type"
                    data={memberTypes}
                    onChange={(value) => setFormData({ ...formData, member_type: value })}
                    selectedOption={formData.member_type}
                    errors={errors}
                  />
                  <MemberRoleAsign
                    label="Member Role"
                    data={roles}
                    optionKey="role_name"
                    valueKey="id"
                    onChange={handleRoleSelection}
                    isMultiSelect={true}
                    selectedOptions={selectedRoleValues}
                    onRoleCreated={handleRoleCreated}
                     onAddRoleClick={() => setIsAddRoleModalOpen(true)}
                  />

              {formData.login_email || formData.login_contact ? (
                <SubmitButton text="Submit" width="full" />
                  ) : (
                <TabButton label="Next" tabIndex={3} handleTabChange={handleTabChange} />
              )}


                </Div>
              )}

            {(!formData.login_email || !formData.login_contact) && activeTab === 3 && (
            <Div>
              <LoginCredential
                formData={formData}
                setFormData={setFormData}
                memberFields={memberFields}
                onNext={() => handleTabChange(2)}
                errors={errors}
              />
              <SubmitButton text="Submit" width="full" />
            </Div>
          )}

        

            </Div>
          </Div>
        </form>
      </Div>
      {/* Render the existing member modal (do not change ComMemberTable component) */}
      <ComMemberTable
        isOpen={showMemberModal}
        onClose={() => setShowMemberModal(false)}
        contactType="org_member"
        commMembers={memberContact}
        onSelect={handleExistingMemberSelect}
      />

        <AddRoleModal
              isOpen={isAddRoleModalOpen}
              onClose={() =>setIsAddRoleModalOpen(false)}
               onRoleCreated={handleRoleCreated}
            />

    </Div>
  );
};

export default OrganizationMemberForm;
