import React from "react";
import Info from "../../../Components/Ui/Info";
import { Link } from "react-router-dom";
import edit2 from "../../../assets/edit-02.png";
import DynamicEditLink from "./DynamicLinkEditButton";
import {
  changeOrgMemberStatus,
  fetchMemberById
} from "../../../redux/slices/api/memberApi";
import { useDispatch, useSelector } from "react-redux";
import { setActiveTabs } from "../../../redux/slices/memberSlice";
import DynamicLinkEditButton from "./DynamicLinkEditButton";

const ComLoginCredentialEditView = ({ selectedMember }) => {
  const dispatch = useDispatch();
  const statusLoading = useSelector((state) => state.member.statusLoading);
  const memberData = useSelector(
    (state) => state.member.selectedMember?.member
  );

  const handleStatusChange = (status) => {
    if (!memberData) return;
    dispatch(
      changeOrgMemberStatus({
        id: memberData.id,
        status,
        member_type: "comm"
      })
    ).then(() => {
      dispatch(fetchMemberById(memberData.id));
    });
  };

  const member = selectedMember.member;
  return (
    <div className="mx-auto border rounded-lg p-3">
      <div className="flex justify-between mb-4">
        <h2 className="text-lg font-bold text-primary">Login Credential</h2>
        <div className="flex justify-between ">
          {member?.is_comm_member == 1 ? (
            <button
              className="mx-2 font-[600] py-1 px-2 rounded-lg bg-primary text-white"
              onClick={() => handleStatusChange(0)} // Change to Inactive
            >
              {statusLoading ? "Updating..." : "Active"}
            </button>
          ) : (
            <button
              className="mx-2 font-[600] py-1 px-2 rounded-lg bg-red-600 text-white"
              onClick={() => handleStatusChange(1)} // Change to Active
            >
              {statusLoading ? "Updating..." : "Inactive"}
            </button>
          )}
          {/* <DynamicEditLink
              basePath="/login-credential-edit/:id"
              id={member?.id}
              onClick={() => dispatch(setActiveTabs(3))}
            /> */}
          {member?.is_comm_member == 1 && (
            <DynamicLinkEditButton
              basePath="/login-credential-edit/:id"
              onClick={() => dispatch(setActiveTabs(3))}

              params={{
                id: member?.id
              }}
            />
          )}
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4">
        {member?.is_comm_member == 1 ? (
          <div className="col-span-3">
            <div className="py-2">
              <Info label="User Name">{member?.username || "---"}</Info>
            </div>
            <div className="py-2">
              <Info label="E-mail/Phone number">
                {member?.login_email || member?.login_contact || "---"}
              </Info>
            </div>
          </div>
        ) : (
          <div className="col-span-3">
            <div className="py-2">
              <Info label="User Name">{"---"}</Info>
            </div>
            <div className="py-2">
              <Info label="E-mail/Phone number">{"---"}</Info>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComLoginCredentialEditView;
