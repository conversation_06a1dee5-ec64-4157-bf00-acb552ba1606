import React from 'react';
import { Link } from 'react-router-dom';

const TowerFloorTable = ({ floors, unitStatusMap }) => {
  return (
    <div className="overflow-x-auto max-h-96 overflow-y-auto">
      <table className="min-w-full table-auto">
        <tbody>
          {floors
            .slice()
            .sort((a, b) => b.floor_no - a.floor_no)
            .map((floor) => (
              <tr key={floor.id} className="border-b">
                <td className="font-semibold text-center text-gray-500 bg-white sticky left-0 z-10">
                  Floor {floor.floor_no}
                </td>
                {floor.units.map((unit) => {
                  const status = unitStatusMap[unit.id];
                  const className = status?.className || "bg-gray-100 text-gray-400";
                  const tooltip = status?.tooltip || "Loading...";
                  return (
                    <td key={unit.id} className="border font-bold text-center p-0 m-0">
                      <Link to={`/unit-details/${unit.id}`}>
                        <div className={`py-3 ${className}`} title={tooltip}>
                          {unit.unit_name}
                        </div>
                      </Link>
                    </td>
                  );
                })}
              </tr>
            ))}
        </tbody>
      </table>
    </div>
  );
};

export default TowerFloorTable; 