import React, { useEffect, useState } from "react";
import { FaFacebookF, FaLinkedinIn } from "react-icons/fa";
import Info from "../../../Components/Ui/Info";
import DottedUserBox from "../../../Components/ImageBox/DottedUserBox";
import axios from "axios";
// import LoginCredentialEditView from "./LoginCredentialEditView";
const api = axios.create({
  baseURL: import.meta.env.VITE_BASE_API
});
const MemberSummary = ({ member }) => {
  const [validFacebook, setValidFacebook] = useState(false);
  const [validLinkedin, setValidLinkedin] = useState(false);

  // Function to check if the URL is valid
  const checkLinkValidity = (url) => {
    try {
      const newUrl = new URL(url);
      return newUrl.protocol === "http:" || newUrl.protocol === "https:";
    } catch (e) {
      return false;
    }
  };

  useEffect(() => {
    if (member?.facebook_profile) {
      setValidFacebook(checkLinkValidity(member?.facebook_profile));
    }
    if (member?.linkedin_profile) {
      setValidLinkedin(checkLinkValidity(member?.linkedin_profile));
    }
  }, [member]);

  return (
    <div className=" w-[304px] border-r border-gray-200 mr-5">
      <h3 className="font-medium pb-4 text-[16px]">Account Management</h3>
      {member?.photo ? (
        <img
          src={`${api.defaults.baseURL}${member?.photo}`}
          alt="User profile photo"
          className="rounded-lg  member-profile-image h-[250px] w-[270px] "
        />
      ) : (
        <DottedUserBox />
      )}

      <div>
        <p className="text-left text-lg pb-[5px] pt-[23px] pb-[12px]">
          {member?.full_name || "Full Name"}
        </p>
        <p className="text-left text-lg text-base">
          {member?.general_contact || "Contact Number"}
        </p>
      </div>
      <div className="my-4">
        <Info label="About">{member?.about_us || "---"}</Info>
      </div>
      <div className="flex justify-left gap-2 py-4">
        {validFacebook ? (
          <a
            href={member?.facebook_profile}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-primary p-3 text-[#fff] rounded"
          >
            <FaFacebookF className="text-lg" />
          </a>
        ) : (
          <div className="bg-gray-300 p-3 text-gray-500 rounded cursor-not-allowed">
            <FaFacebookF className="text-lg" />
          </div>
        )}

        {validLinkedin ? (
          <a
            href={member?.linkedin_profile}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-primary p-3 text-[#fff] rounded"
          >
            <FaLinkedinIn className="text-lg" />
          </a>
        ) : (
          <div className="bg-gray-300 p-3 text-gray-500 rounded cursor-not-allowed">
            <FaLinkedinIn className="text-lg" />
          </div>
        )}
      </div>
    </div>
  );
};

export default MemberSummary;
