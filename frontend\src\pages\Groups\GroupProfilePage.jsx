import React, { useEffect } from "react";
import PageContainer from "../../Components/Ui/PageContainer";
import Row from "../../Components/Ui/Row";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { fetchGroupDetail, toggleGroupStatus } from "../../redux/slices/groups/groupSlice";
import ArrowHeading from "../../Components/HeadingComponent/ArrowHeading";
import LoadingAnimation from "../../Components/Loaders/LoadingAnimation";
import ContentBox from "../../Components/Ui/ContentBox";
import Button from "../../Components/FormComponent/ButtonComponent/Button";
import { Paragraph } from "../../Components/Ui/Paragraph";
import { FaEdit } from "react-icons/fa";
import GroupProfile from "../../Features/Groups/GroupProfile/GroupProfile";
import { checkPermission } from "../../utils/permissionUtils"; // Newly added import

const GroupProfilePage = () => {
  const { id } = useParams(); // Get group id from URL.
  const dispatch = useDispatch();
  const { groupDetail, loading, error } = useSelector((state) => state.group);
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      dispatch(fetchGroupDetail(id));
    }
  }, [dispatch, id]);

  const handleToggleStatus = async () => {
    // Check central permission before toggling the group status
    const permissionGranted = await checkPermission("org", 8);
    if (!permissionGranted) {
      navigate("/not-authorized");
      return;
    }
    dispatch(toggleGroupStatus(id));
  };

  if (loading || !groupDetail) {
    return (
      <ContentBox className="flex justify-center items-center">
        <LoadingAnimation />
      </ContentBox>
    );
  }

  if (error) {
    return <Paragraph>Error: {error}</Paragraph>;
  }

  const { group_name, is_active } = groupDetail;

  return (
    <PageContainer>
      <Row className="justify-between mb-4">
        <ArrowHeading
          title={group_name ? group_name : "Group Details"}
          onNext={() => navigate("/groups")} // Always navigate to group list page when going back
          size="xl"
          color="black"
          fontWeight="semibold"
        />
        <Row className="items-center space-x-4">
          {is_active ? (
            <Button size="lg" onClick={handleToggleStatus}>
              Active
            </Button>
          ) : (
            <Button size="lg" variant="red" onClick={handleToggleStatus}>
              Inactive
            </Button>
          )}
          <Button
            size="lg"
            variant="transparent"
            icon={FaEdit}
            onClick={() => navigate(`/addGroup/${id}`)} // Navigate to edit group page
          >
            Edit
          </Button>
        </Row>
      </Row>
      <ContentBox>
        <GroupProfile groupDetail={groupDetail} />
      </ContentBox>
    </PageContainer>
  );
};

export default GroupProfilePage;
