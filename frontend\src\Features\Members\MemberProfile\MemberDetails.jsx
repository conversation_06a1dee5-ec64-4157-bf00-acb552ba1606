import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { setActiveTabs } from "../../../redux/slices/memberSlice";
import ProfileInformationView from "./ProfileInformationView";
import OwnershipDetailsView from "./OwnershipDetailsView";
import ResidentDetailsView from "./ResidentDetailsView";
import OrgLoginCredentialEditView from "./OrgLoginCredentialEditView";
import ComLoginCredentialEditView from "./ComLoginCredentialEditView";
import OrganizationMemberInformationView from "./OrganizationMemberInformationView";
import StaffDetailsView from "./StaffDetailsView";

const MemberDetails = ({ selectedMember }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const member = selectedMember?.member || {};
  const residents = selectedMember?.residents || [];
  const owners = selectedMember?.owners || [];
  const staff = selectedMember?.staff || [];

  // Debug logging
  console.log('Full Member Object:', member);
  console.log('Member History:', {
    org_member_ever_created: member.org_member_ever_created,
    comm_member_ever_created: member.comm_member_ever_created,
    is_org_member: member.is_org_member,
    is_comm_member: member.is_comm_member
  });

  // Create dynamic tabConfig based on member's history
  const tabConfig = [
    { id: 1, label: "Profile Information" },
    ...(member.org_member_ever_created ? [{ id: 2, label: "Organization Member" }] : []),
    ...(member.comm_member_ever_created ? [{ id: 3, label: "Community Member" }] : []),
  ];

  // 1) Read current active tab from Redux
  const reduxActiveTab = useSelector((state) => state.member.activeTabs);
  // 2) Local state initialized from Redux
  const [activeTab, setActiveTab] = useState(reduxActiveTab);

  // 3) Sync local state whenever Redux changes
  useEffect(() => {
    setActiveTab(reduxActiveTab);
  }, [reduxActiveTab]);

  const handleTabChange = (tabNumber) => {
    setActiveTab(tabNumber);
    dispatch(setActiveTabs(tabNumber));
  };

  const renderOwnershipSection = () => {
    if (!owners.length) {
      return <p className="text-gray-500 py-4">No ownership data available.</p>;
    }
    return (
      <div className="ownership-section border rounded-lg pb-3 my-3">
        <h2 className="text-lg font-bold text-primary py-2 m-1 px-5">
          Ownership Details
        </h2>
        <div className="space-y-4">
          {owners.map((unit) => (
            <OwnershipDetailsView
              key={unit.id || unit._id}
              unit={unit}
              className="ownership-item"
            />
          ))}
        </div>
      </div>
    );
  };

  const renderResidentDetails = () => {
    if (!residents.length) {
      return <p className="text-gray-500 py-4">No resident data available.</p>;
    }
    return (
      <div className="space-y-4">
        {residents.map((resident) => (
          <ResidentDetailsView
            key={resident.id}
            residentData={resident}
            className="ownership-item"
          />
        ))}
      </div>
    );
  };

  const renderStaffDetails = () => {
    if (!staff.length) {
      return <p className="text-gray-500 py-4">No Staff data available.</p>;
    }
    return (
      <div>
        {staff.map((unit_staff) => (
          <StaffDetailsView
            key={unit_staff.id}
            unit_staff={unit_staff}
            className="ownership-item"
          />
        ))}
      </div>
    );
  };

  return (
    <div className="w-[698px]">
      <div className="flex mb-4 bg-subprimary" role="tablist">
        {tabConfig.map(({ id, label }) => (
          <button
            key={id}
            role="tab"
            aria-selected={activeTab === id}
            className={`flex-1 w-full py-2 font-semibold ${
              activeTab === id ? "border border-primary" : "border border-white"
            }`}
            onClick={() => handleTabChange(id)}
          >
            {label}
          </button>
        ))}
      </div>

      <div role="tabpanel">
        {activeTab == 1 && (
          <ProfileInformationView
            memberData={member}
            onEditClick={() => navigate(`/general-information-edit/${member?.id}`)}
          />
        )}

        {activeTab == 2 && (
          <div className="mx-auto">
            <OrgLoginCredentialEditView
              selectedMember={selectedMember}
              handleLoginCredentialEditClick={() =>
                navigate(`/login-credential-edit/${member?.id}`)
              }
            />
            {member?.is_org_member == 1 && (
              <OrganizationMemberInformationView member={member} />
            )}
          </div>
        )}

        {activeTab == 3 && (
          <>
            {(residents.length || owners.length || staff.length) && (
              <ComLoginCredentialEditView selectedMember={selectedMember} />
            )}
            {renderOwnershipSection()}
            {renderResidentDetails()}
            {renderStaffDetails()}
          </>
        )}
      </div>
    </div>
  );
};

MemberDetails.propTypes = {
  selectedMember: PropTypes.shape({
    member: PropTypes.object,
    residents: PropTypes.array,
    owners: PropTypes.array,
    staff: PropTypes.array,
  }),
};

MemberDetails.defaultProps = {
  selectedMember: {
    member: {},
    residents: [],
    owners: [],
    staff: [],
  },
};

export default MemberDetails;
