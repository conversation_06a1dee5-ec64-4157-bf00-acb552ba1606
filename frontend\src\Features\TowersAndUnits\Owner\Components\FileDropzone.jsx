// File: FileDropzone.jsx
import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import { FaTimes } from "react-icons/fa";

const baseURL = import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000";

const normalizeUrl = (path) => {
  const trimmedBase = baseURL.replace(/\/+$/, "");
  const trimmedPath = path.replace(/^\/+/, "");
  return `${trimmedBase}/${trimmedPath}`;
};

const FileDropzone = ({
  onDrop,
  files = [],
  docLinks = [],
  onRemove,
  onUpdate,
  disabled = false,
  readOnly = false,
  showRemoveButton = true,
  showUploadButton = true,
  showDropzone = true,
  hideDropzoneMessage = false
}) => {
  const [fileError, setFileError] = useState("");
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

  const clearError = () => setTimeout(() => setFileError(""), 5000);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      "application/pdf": [".pdf"],
      "image/*": [".jpg", ".jpeg", ".png", ".gif"]
    },
    maxSize: MAX_FILE_SIZE,
    multiple: true,
    disabled: disabled || readOnly,
    onDrop: (acceptedFiles) => {
      const validFiles = acceptedFiles.filter(
        (file) => file.size <= MAX_FILE_SIZE
      );
      const oversizedFiles = acceptedFiles.filter(
        (file) => file.size > MAX_FILE_SIZE
      );

      if (oversizedFiles.length > 0) {
        setFileError("File size must not exceed 5MB.");
        clearError();
      }

      if (validFiles.length > 0) {
        onDrop(validFiles);
      }
    },
    onDropRejected: (fileRejections) => {
      const oversized = fileRejections.some((rejection) =>
        rejection.errors.some((err) => err.code === "file-too-large")
      );
      setFileError(
        oversized ? "File size must not exceed 5MB." : "Invalid file type."
      );
      clearError();
    }
  });

  const isImage = (file) => file?.type?.startsWith("image/");

  const handleFileUpdate = (idx, event) => {
    if (disabled || readOnly) return;
    
    const file = event.target.files[0];
    if (!file) return;

    if (file.size > MAX_FILE_SIZE) {
      setFileError("File size must not exceed 5MB.");
      clearError();
      return;
    }

    onUpdate(idx, file);
  };

  return (
    <div>
      {showDropzone && (
        <div
          {...getRootProps()}
          className={`border-dashed border-2 rounded p-4 ${
            !disabled && !readOnly ? "cursor-pointer" : "cursor-default"
          } text-center transition-all duration-200 ${
            isDragActive ? "border-[#3D9D9B] bg-[#f0fdfc]" : "border-gray-300"
          }`}
        >
          <input {...getInputProps()} />
          {!hideDropzoneMessage && (
            <p className="text-sm text-gray-600">
              {isDragActive
                ? "Drop the files here..."
                : "Drag & drop files here, or click to select (PDF, JPG, PNG, GIF)"}
            </p>
          )}
        </div>
      )}

      {(files.length > 0 || docLinks.length > 0) && (
        <div className={`mt-4 grid grid-cols-2 gap-3 ${!showDropzone ? 'mt-0' : ''}`}>
          {files.map((file, idx) => (
            <div
              key={idx}
              className="relative p-2 border rounded shadow-sm text-left text-sm"
            >
              {showRemoveButton && !disabled && !readOnly && (
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    onRemove(idx, "file");
                  }}
                  className="absolute top-1 right-1 bg-white rounded-full p-1 shadow hover:bg-red-100 z-10"
                >
                  <FaTimes className="text-red-500 text-sm" />
                </button>
              )}

              {isImage(file) ? (
                <img
                  src={URL.createObjectURL(file)}
                  alt={file.name}
                  className="w-full h-[120px] object-cover rounded mb-1"
                />
              ) : (
                <p className="truncate mt-6 pl-4">📄 {file.name}</p>
              )}
            </div>
          ))}

          {docLinks.map((linkObj, idx) => {
            const fullLink = normalizeUrl(linkObj.url || linkObj);
            const isImageLink = /\.(jpeg|jpg|png|gif)$/i.test(fullLink);

            return (
              <div
                key={`link-${idx}`}
                className="relative p-2 border rounded shadow-sm text-left text-sm"
              >
                {showRemoveButton && !disabled && !readOnly && (
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemove(idx, "docLink");
                    }}
                    className="absolute top-1 right-1 bg-white rounded-full p-1 shadow hover:bg-red-100 z-10"
                  >
                    <FaTimes className="text-red-500 text-sm" />
                  </button>
                )}

                <div className="relative">
                  {isImageLink ? (
                    <img
                      src={fullLink}
                      alt={`doc-${idx}`}
                      className="w-full h-[120px] object-cover rounded mb-1"
                    />
                  ) : (
                    <a
                      href={fullLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block truncate text-blue-600 hover:underline mt-6 pl-4"
                    >
                      📎 {fullLink.split("/").pop()}
                    </a>
                  )}

                  {showUploadButton && !disabled && !readOnly && (
                    <input
                      type="file"
                      onChange={(e) => handleFileUpdate(idx, e)}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      accept=".pdf,.jpg,.jpeg,.png,.gif"
                    />
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {fileError && (
        <div className="mt-3 p-2 bg-red-100 text-red-700 border border-red-300 rounded text-sm">
          {fileError}
        </div>
      )}
    </div>
  );
};

export default FileDropzone;
