import {useState,useEffect} from "react";
import { useDispatch, useSelector } from "react-redux";
import { createCompany } from "./../../../../redux/slices/api/companyApi";
import { clearMessage } from "../../../../redux/slices/companySlice";
import { setCreatedMember } from "../../../../redux/slices/owner/ownerSlice";
import { useNavigate } from "react-router-dom";


const useCompanySubmit = (formData, validateForm, activeTab, unitId) => {
const dispatch = useDispatch();
const { message, error,company} = useSelector((state) => state.company);
const [showMessage, setShowMessage] = useState(false);
const [loading, setLoading] = useState(false);




  const handleSubmitCompany = (e) => {
    e.preventDefault();

    const validationPassed = validateForm(formData, activeTab);
    if (!validationPassed) return;

    // Normalize required fields
    formData.full_name = formData.company_name;
    formData.unit_id = unitId;
    formData.is_comm_member = 1;
    formData.comm_member_ever_created = 1;
    formData.is_org_member = 0;

   
    // if (formData.login === "email" ) {
  
      //  formData.delivery_method = formData.email || formData.login_email||formData.general_email||formData.contact||formData.login_contact||formData.general_contact;
     
     
     
      if(formData.login=='email'){
        formData.login_email = formData.email || formData.login_email|| formData.general_email;
      }

       if(formData.login=='contact'){

        formData.login_contact= formData.contact||formData.login_contact|| formData.general_contact;

       }

      //  formData.delivery_method = formData.email || formData.login_email||formData.contact||formData.login_contact;

       
    
   

    const formDataObject = new FormData();

    for (const key in formData) {
      if (
        formData.hasOwnProperty(key) &&
        key !== "members_role" &&
        key !== "memberType"
      ) {
        const value = formData[key];
        if (key === "photo") {
          if (value instanceof File || value instanceof Blob) {
            formDataObject.append("photo", value);
          } else if (!value) {
            formDataObject.append("photo_removed", "Removed");
          }
        } else {
          formDataObject.append(key, value);
        }
      }
    }

    setLoading(true);

    dispatch(createCompany(formDataObject))
      .then((action) => {
        setLoading(false);
  //         const companys = useSelector((state) => {
    
  //           state.company_data
            
  //           // console.log('dsf',state)
  //           // console.log('data',state.company_data)
            
  // });
        // console.log('after submit_com',companys)
        // console.log('after submit',action.payload)
        // console.log('after submit',company_data)
        // const member = action?.payload?.member;
        // if (member?.id) {
        //   dispatch(
        //     setCreatedMember({
        //       id: member.id,
        //       full_name: member.full_name
        //     })
        //   );
        // }
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {

    // console.log('company_data',company)
    if (message || error) {
      setShowMessage(true);
    }
  }, [message, error]);

  const handleDismissMessage = () => {
    dispatch(clearMessage());
    setShowMessage(false);
  };


  return {
    handleSubmitCompany,
    loading,
    showMessage,
    message,
    error,
    handleDismissMessage
  };
};

export default useCompanySubmit;
