import { Outlet } from "react-router-dom";
import { useState } from "react";
import Sidebar from "../pages/Dashboard/SideBar";
import Header from "../pages/Dashboard/Header";

const HEADER_HEIGHT = 64; // Change if your header height is different

const Main = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="h-screen w-screen overflow-hidden bg-gray-200 flex flex-col">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 z-50 h-[64px]">
        <Header toggleSidebar={toggleSidebar} />
      </div>

      {/* Body container with padding-top to avoid header overlap */}
      <div className="flex flex-1 pt-[64px]">
        {/* Sidebar - positioned below header */}
        <aside
          className={`fixed top-[64px] left-0 bottom-0 z-40 bg-gray-200 transform ${
            isSidebarOpen ? "translate-x-0 w-[20%]" : "-translate-x-full"
          } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:w-[20%]`}
        >
          <Sidebar isOpen={isSidebarOpen} />
        </aside>

        {/* Backdrop for mobile */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
            onClick={toggleSidebar}
          ></div>
        )}

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto h-[calc(97vh-64px)] mt-[35px] ">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default Main;
