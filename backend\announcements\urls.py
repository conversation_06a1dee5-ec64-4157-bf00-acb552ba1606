from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    AnnouncementViewSet,
    AnnouncementAttachmentViewSet,
    TowerListView,
    UnitListView
)

# Create router for viewsets
router = DefaultRouter()
router.register(r'announcements', AnnouncementViewSet, basename='announcement')
router.register(r'attachments', AnnouncementAttachmentViewSet, basename='attachment')

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Additional API endpoints
    path('towers/', TowerListView.as_view(), name='tower-list'),
    path('units/', UnitListView.as_view(), name='unit-list'),
]
