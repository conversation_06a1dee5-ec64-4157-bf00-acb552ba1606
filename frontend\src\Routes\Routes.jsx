// src/routes/route.js
import { createBrowserRouter } from "react-router-dom";
import ProtectedRoute from "./ProtectedRoute";

// Auth Pages
import Login from "../Authentication/Login/Login";
import ForgotPassword from "../Authentication/ForgotPassword/ForgotPassword";
import VerifyCode from "../Authentication/VerifyCode/VerifyCode";
import SetNewPassword from "../Authentication/SetNewPassword/SetNewPassword";
import Logindummy from "../Authentication/Login/Logindummy";
import About from "../Authentication/Login/About";

// Shared
import Dashboard from "../Layout/Dashboard";
import MessageBox from "../Components/MessageBox/MessageBox";
import NotAuthorized from "../Components/NotAuthorized/NotAuthorized";

// Tower & Unit Features
import AddTower from "../Features/TowersAndUnits/Towers/pages/AddTower";
import ViewTowers from "../Features/TowersAndUnits/Towers/pages/ViewTowers";
import EditTower from "../Features/TowersAndUnits/Towers/pages/EditTower";

// import AddUnit from "../Features/TowersAndUnits/Units/AddUnits/AddUnit";
import AddOwner from "../Features/TowersAndUnits/Owner/AddOwner/AddOwner";
import ChangeOwner from "../Features/TowersAndUnits/Owner/ChangeOwner/ChangeOwner";
import OwnerDetails from "../Features/TowersAndUnits/Owner/OwnerDetails/OwnerDetails";
import AddResident from "../Features/TowersAndUnits/Resident/AddResident/AddResident";
import AddUnitStaff from "../Features/TowersAndUnits/UnitStaff/AddUnitStaff/AddUnitStaff";
import UnitDetails from "../Features/TowersAndUnits/UnitDetails/pages/UnitDetails";

// Member & Group Management
import AddMemberPage from "../pages/AddMemberPage";
import MemberListPage from "../pages/MemberListPage";
import MemberProfilePage from "../pages/Members/MemberProfilePage";
import GeneralInformationEditPage from "../pages/Members/GeneralInformationEditPage";
import MemberTypeAndRoleEditPage from "../pages/Members/MemberTypeAndRoleEditPage";

import GroupsPage from "../pages/Groups/GroupsPage";
import GroupProfilePage from "../pages/Groups/GroupProfilePage";
import AddGroup from "../Features/Groups/AddGroup/AddGroup";

// Role Management
import RoleList from "../Features/Roles/RoleList/RoleList";
import AddRole from "../Features/Roles/AddRole/AddRole";
import RoleProfile from "../Features/Roles/RoleProfile/RoleProfile";

// Login Credentials
import LoginCredentialEdit from "../Features/Login/LoginCredential/LoginCredentialEdit";

// Misc
import DemoPage from "../pages/DemoPage";
import ResidentDetails from "../Features/TowersAndUnits/Resident/ResidentDetails/ResidentDetails";
import ResidentInfoEdit from "../Features/TowersAndUnits/Resident/ResidentInfoEdit/ResidentInfoEdit";
import UnitEdit from "../Features/TowersAndUnits/Units/AddUnits/UnitEdit";
import ResidentCheck from "../Features/TowersAndUnits/Resident/Components/ResidentCheck";
import CommunityMemberList from "../Features/Members/CommunityMember/CommunityMemberList";
import UnitStaffInformationEdit from "../Features/Members/MemberEdit/UnitStaffInformationEdit/UnitStaffInformationEdit";
import Main from "../Layout/Main";
// import CommunityMemberList from "../Features/CommunityMember/CommunityMemberList";

import AnnouncementList from "../Features/CommunicationPortal/Announcements/AnnouncementList";
import CreateAnnouncement from "../Features/CommunicationPortal/Announcements/CreateAnnouncementMain";
import EditAnnouncement from "../Features/CommunicationPortal/Announcements/EditAnnouncementMain";




// Permission constants for Member-related actions
const create_Member = 1;  // Permission to create a new member
const edit_Member = 2;    // Permission to edit an existing member
const view_Member = 3;    // Permission to view member details

// Permission constants for Role-related actions
const create_Role = 4;    // Permission to create a new role
const edit_Role = 5;      // Permission to edit an existing role
const view_Role = 6;      // Permission to view role details

// Permission constants for Group-related actions
const create_Group = 7;   // Permission to create a new group
const edit_Group = 8;     // Permission to edit an existing group
const View_Group = 9;     // Permission to view group details

// Permission constants for Tower-related actions
const create_Tower = 10;  // Permission to create a new tower
const edit_Tower = 11;    // Permission to edit an existing tower
const view_Tower = 12;    // Permission to view tower details


export const router = createBrowserRouter([
  {
    path: "/",
    element: <Main />,
    children: [
      // Default route
      {
        path: "/",
        element: (
          <ProtectedRoute requiredPermission={view_Member}>
            <Dashboard />
            </ProtectedRoute>
        )
      },

      // Members
      {
        path: "member-list",
        element: (
          <ProtectedRoute requiredPermission={view_Member}>
          <MemberListPage />
           </ProtectedRoute>
        )
      },
      {
        path: "create-member",
        element: (
          <ProtectedRoute requiredPermission={create_Member}>
            <AddMemberPage />
          </ProtectedRoute>
        )
      },
      {
        path: "member-profile/:id",
        element: (
          <ProtectedRoute requiredPermission={view_Member}>
            <MemberProfilePage />
          </ProtectedRoute>
        )
      },
      {
        path: "general-information-edit/:id",
        element: (
          <ProtectedRoute requiredPermission={edit_Member}>
            <GeneralInformationEditPage />
          </ProtectedRoute>
        )
      },
      {
        path: "MemberTypeAndRoleEdit/:id",
        element: (
          <ProtectedRoute requiredPermission={edit_Member}>
            <MemberTypeAndRoleEditPage />
          </ProtectedRoute>
        )
      },

      // Groups
      {
        path: "groups",
        element: (
          <ProtectedRoute requiredPermission={View_Group}>
            <GroupsPage />
          </ProtectedRoute>
        )
      },
      {
        path: "addGroup/:id?",
        element: (
          <ProtectedRoute requiredPermission={create_Group}>
            <AddGroup />
          </ProtectedRoute>
        )
      },
      {
        path: "groupProfile/:id",
        element: (
          <ProtectedRoute requiredPermission={View_Group}>
            <GroupProfilePage />
          </ProtectedRoute>
        )
      },

      // Roles
      {
        path: "roleList",
        element: (
          <ProtectedRoute requiredPermission={view_Role}>
            <RoleList />
          </ProtectedRoute>
        )
      },
      {
        path: "addRole/:id?",
        element: (
          <ProtectedRoute requiredPermission={create_Role}>
            <AddRole />
          </ProtectedRoute>
        )
      },
      {
        path: "roleProfile/:id",
        element: (
          <ProtectedRoute requiredPermission={view_Role}>
            <RoleProfile />
          </ProtectedRoute>
        )
      },

      // Towers
      {
        path: "ViewTowers",
        element: <ViewTowers />
      },
      {
        path: "addTower",
        element: <AddTower />
      },
      {
        path: "editTower/:id",
        element: <EditTower />
      },

      // Units & Details
      {
        path: "add-unit/:id",
        element: <UnitEdit />
      },
      {
        path: "unit-details/:id",
        element: <UnitDetails />
      },


      // Unit Owner, Resident, Staff
      {
        path: "unit/:unitId/add-owner",
        element: <AddOwner />
      },
      // Unit Owner, Resident, Staff
      {
        path: "unit/:unitId/change-owner",
        element: <ChangeOwner />
      },
      {
        path: "resident_info_edit/:unitId/:residentId",
        element: <ResidentInfoEdit />
      }
      ,
      {
        path: "owner-details/:unitId/:ownerId",
        element: <OwnerDetails />
      },
      {
        path: "addResident/:id",
        element: <AddResident />
      },
      {
        path: "resident-details/:unitId/:residentId",
        element: <ResidentDetails />,
      },
      {
        path: "addUnitStaff/:id",
        element: <AddUnitStaff />
      },

      // Login Credentials
      {
        path: "login-credential-edit/:id",

        element: <LoginCredentialEdit />
      },

      // Misc Pages
      {
        path: "demoPage",
        element: <DemoPage />
      },

      {
        path: "announcements",
        element: <AnnouncementList />,
      },
      {
        path: "create-announcement",
        element: <CreateAnnouncement />,
      },
      {
        path: "edit-announcement/:id",
        element: <EditAnnouncement />,
      },

      {
        path: "about",
        element: <About />
      },
      {
        path: "/community-memberList",
        element: <CommunityMemberList />
      },
      {
        path: "unit-staff-edit/:staffid/:staffstatus",
        element: <UnitStaffInformationEdit />
      },
    ]
  },

  // Public/Unprotected Routes
  {
    path: "/login",
    element: <Login />
  },

  {
    path: "forgotPassword",
    element: <ForgotPassword />
  },
  {
    path: "verifyCode",
    element: <VerifyCode />
  },
  {
    path: "setNewPassword",
    element: <SetNewPassword />
  },

  {
    path: "logindummy",
    element: <Logindummy />
  },
  {
    path: "messageBox",
    element: <MessageBox />
  },
  {
    path: "not-authorized",
    element: <NotAuthorized />
  }
]);
