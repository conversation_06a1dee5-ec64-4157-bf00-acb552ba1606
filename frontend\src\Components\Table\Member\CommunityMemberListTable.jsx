import { FaEye } from "react-icons/fa";
import { <PERSON> } from "react-router-dom";
import user1 from "../../../assets/user/user.png";
import { setActiveTabs } from "../../../redux/slices/memberSlice";
import { useDispatch } from "react-redux";

const baseURL = import.meta.env.VITE_BASE_API;

const CommunityMemberListTable = ({ members, error }) => {
  const hasMembers = Array.isArray(members) && members.length > 0;

  const dispatch = useDispatch();

  return (
    <div>
      <table className="w-full text-sm text-left rtl:text-right">
        <thead className="bg-subprimary shadow-lg border-b border-subprimary">
          <tr>
            <th className="px-3 font-[700] py-2 text-base text-left">Name</th>
            <th className="px-3 font-[700] py-2 text-base text-left">
              Contact
            </th>
            <th className="px-3 font-[700] py-2 text-base text-left">Type</th>
            <th className="px-3 font-[700] py-2 text-base text-left">Email</th>
            <th className="px-3 font-[700] py-2 text-base text-left">
              Occupation
            </th>
            <th className="px-3 font-[700] py-2 text-base text-left">Tower</th>
            <th className="px-3 font-[700] py-2 text-base text-left">Unit</th>
            <th className="px-3 font-[700] py-2 text-base text-left">Status</th>
            <th className="px-3 font-[700] py-2 text-base text-left">Action</th>
          </tr>
        </thead>
        <tbody>
          {!hasMembers ? (
            <tr>
              <td colSpan="9" className="text-center text-gray-500 py-4">
                {error?.message === "No results found"
                  ? "No results found"
                  : "No members available"}
              </td>
            </tr>
          ) : (
            members?.map((member, index) => (
              <tr key={index} className="bg-white border-b hover:bg-gray-50">
                <td className="px-3 py-[10px] font-medium flex items-center gap-2">
                  <img
                    src={member.photo ? `${baseURL}${member.photo}` : user1}
                    alt="User"
                    className="w-10 h-10 rounded-full"
                  />
                  {member.full_name}
                </td>
                <td className="px-3 py-2 text-base text-left">
                  {member.general_contact}
                </td>
                <td className="px-3 py-2 text-base text-left">{member.type}</td>
                <td className="px-3 py-2 text-base text-left">
                  {member.general_email}
                </td>
                <td className="px-3 py-2 text-base text-left">
                  {member.occupation}
                </td>
                <td className="px-3 py-2 text-base text-left">
                  {member.tower}
                </td>
                <td className="px-3 py-2 text-base text-left">{member.unit}</td>
                <td className="px-3 py-2 text-base text-left">
                  <span
                    className={`mx-2 font-[600] py-1 px-2 rounded-8 text-white ${
                      member.status === "Active" ? "bg-primary" : "bg-secondary"
                    }`}
                  >
                    {member.status}
                  </span>
                </td>
                <td className="px-3 py-2 text-base text-left">
                  {/* <Link to={`/member-profile/${member.id}`}>
                  onClick={() => dispatch(setActiveTabs(1))}

                    <FaEye className="w-[25px] h-[20px] text-primary" />
                  </Link> */}
                  <Link
                    to={{
                      pathname: `/member-profile/${member.id}`,
                      state: { from: location.pathname }
                    }}
                    onClick={() => dispatch(setActiveTabs(1))}
                  >
                    <FaEye color="#3D9D9B" className="w-[25px] h-[20px]" />
                  </Link>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default CommunityMemberListTable;
