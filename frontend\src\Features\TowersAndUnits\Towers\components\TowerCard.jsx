import React from 'react';
import { Link } from 'react-router-dom';
import { FaTrash } from 'react-icons/fa6';
import TowerFloorTable from './TowerFloorTable';

const TowerCard = ({ tower, unitStatusMap, onDeleteClick }) => {
  return (
    <div className="bg-white rounded-[24px] p-6">
      <div className="mb-4 p-3 flex justify-between items-center rounded-[8px] bg-[#3C9D9B1A]">
        <p className="text-[20px] font-semibold">
          {tower.tower_name} (Tower {tower.tower_number})
        </p>
        <div>
          <button className="p-3">
            <Link to={`/EditTower/${tower.id}`}>
              <img src="./edit-03.png" alt="Edit" />
            </Link>
          </button>
          <button onClick={() => onDeleteClick(tower.id)}>
            <FaTrash className="text-red-500 cursor-pointer text-xl" />
          </button>
        </div>
      </div>

      <div className="border-[#DCE0E5] border-[1px] rounded-[8px] p-4">
        <TowerFloorTable
          floors={tower.floors}
          unitStatusMap={unitStatusMap}
        />
      </div>
    </div>
  );
};

export default TowerCard; 