import React, { useEffect, useRef, useState } from "react";
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FaMinus, FaPlus } from "react-icons/fa";

import {
  createOwner,
  updateOwner,
  deleteOwner,
  fetchOwnerList,
  clearCreatedMember,
  clearMessage
} from "../../../../redux/slices/owner/ownerSlice";

import {
  ownerValidationSchema,
  getOrdinal,
  formatDate
} from "../utils/ownerUtils";

import { Paragraph } from "../../../../Components/Ui/Paragraph";
import NumberInputComponent from "../../../../Components/FormComponent/NumberInputComponent";
import FileDropzone from "../Components/FileDropzone";
import AddMemberForm from "../Components/Modals/AddMemberForm";
import AddCompany from "../AddCompany/AddCompany";
import MemberSearchAutocomplete from "../../../../Components/MemberSearchAutocomplete/MemberSearchAutocomplete";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import ConfirmationMessageBox from "../../../../Components/MessageBox/ConfirmationMessageBox";

const ChangeOwnerForm = ({ unitId }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const ownerState = useSelector((state) => state.owner);

  const [searchTerms, setSearchTerms] = useState({});
  const [isMemberModalOpen, setIsMemberModalOpen] = useState(false);
  const [showCompanyModal, setShowCompanyModal] = useState(false);
  const [docsToDelete, setDocsToDelete] = useState({});
  const [localError, setLocalError] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [indexToDelete, setIndexToDelete] = useState(null); // Track which owner index

  const ownerRefs = useRef([]);

  const {
    control,
    register,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(ownerValidationSchema),
    defaultValues: { owners: [] }
  });

  const { fields, append, remove, replace } = useFieldArray({
    control,
    name: "owners"
  });

  const owners = watch("owners");

  const handleRemoveOwner = (index) => {
    const ownerId = watch(`owners.${index}.id`);

    if (ownerId) {
      // Ask confirmation first
      setIndexToDelete(index);
      setShowConfirmation(true);
    } else {
      // Just remove immediately if not saved yet
      remove(index);
      ownerRefs.current.splice(index, 1);
    }
  };

  const handleConfirmDelete = async () => {
    if (indexToDelete !== null) {
      const ownerId = watch(`owners.${indexToDelete}.id`);
      try {
        await dispatch(deleteOwner(ownerId)).unwrap();
        remove(indexToDelete);
        ownerRefs.current.splice(indexToDelete, 1);
        await dispatch(fetchOwnerList(unitId)); // Optional: refresh list
      } catch (error) {
        console.error("🚨 Failed to delete owner:", error);
      }
    }
    setShowConfirmation(false);
    setIndexToDelete(null);
  };

  const handleCancelDelete = () => {
    setShowConfirmation(false);
    setIndexToDelete(null);
  };

  // Fetch owners for the given unit when component mounts
  useEffect(() => {
    dispatch(fetchOwnerList(unitId));
    dispatch(clearMessage());
  }, [dispatch, unitId]);

  useEffect(() => {
    const ownersList = ownerState.ownerList?.owners || [];
    if (ownersList.length) {
      const transformed = ownersList.map((owner) => ({
        id: owner.id,
        memberId: owner.member.id,
        ownershipPercentage: owner.ownership_percentage,
        dateofOwnership: new Date(owner.date_of_ownership)
          .toISOString()
          .split("T")[0],
        document: [],
        docLinks:
          owner.docs?.map((doc) => ({ id: doc.id, url: doc.url })) || [],
        ownershipTransferFromId: owner.ownership_transfer_from?.id || "",
        isExisting: true
      }));

      replace(transformed);

      const initialTerms = {};
      ownersList.forEach((owner, idx) => {
        initialTerms[idx] = owner.member.full_name;
        initialTerms[`from_${idx}`] =
          owner.ownership_transfer_from?.full_name || "";
      });
      setSearchTerms(initialTerms);
    }
  }, [ownerState.ownerList, replace]);

  // Updates form fields when a new member is added
  useEffect(() => {
    if (ownerState.createdMember) {
      const emptyIndex = fields.findIndex(
        (_, index) => !watch(`owners.${index}.memberId`)
      );
      const indexToUse = emptyIndex !== -1 ? emptyIndex : fields.length;

      if (emptyIndex === -1) {
        append({
          memberId: "",
          ownershipPercentage: "",
          dateofOwnership: "",
          document: [],
          docLinks: [],
          ownershipTransferFromId: ""
        });
      }

      setTimeout(() => {
        setValue(`owners.${indexToUse}.memberId`, ownerState.createdMember.id, { shouldValidate: true });
        setSearchTerms((prev) => ({
          ...prev,
          [indexToUse]: ownerState.createdMember.full_name
        }));

        ownerRefs.current[indexToUse]?.current?.scrollIntoView({
          behavior: "smooth"
        });
        ownerRefs.current[indexToUse]?.current?.querySelector("input")?.focus();

        dispatch(clearCreatedMember());
        dispatch(clearMessage());
        setIsMemberModalOpen(false);
        setShowCompanyModal(false);
      }, 100);
    }
  }, [ownerState.createdMember, fields, watch, setValue, append, dispatch]);

  // Updates search terms whenever owners or fields change
  useEffect(() => {
    if (!owners.length) return;

    const newTerms = {};
    owners.forEach((owner, idx) => {
      if (owner.memberId) {
        const fullName =
          ownerState.ownerList?.owners?.find(
            (o) => o.member.id === owner.memberId
          )?.member.full_name || "";
        newTerms[idx] = fullName;
      }
      if (owner.ownershipTransferFromId) {
        const fromFullName =
          ownerState.ownerList?.owners?.find(
            (o) => o.member.id === owner.memberId
          )?.ownership_transfer_from?.full_name || "";

        newTerms[`from_${idx}`] = fromFullName;
      }
    });

    setSearchTerms(newTerms);
  }, [owners, ownerState.ownerList]);

  const onSubmit = async (data) => {
    console.log("➡️ Submitting form data:", data);

    const totalOwnership = data.owners.reduce(
      (sum, owner) => sum + parseFloat(owner.ownershipPercentage || 0),
      0
    );

    if (totalOwnership > 100) {
      setLocalError("Total ownership percentage cannot exceed 100%.");
      return;
    }

    const memberIds = data.owners.map((owner) => owner.memberId);
    const uniqueMemberIds = new Set(memberIds);
    if (memberIds.length !== uniqueMemberIds.size) {
      setLocalError("Duplicate members are not allowed.");
      return;
    }

    try {
      const ownerPromises = data.owners.map(async (owner, index) => {
        if (
          !owner.memberId ||
          !owner.ownershipPercentage ||
          !owner.dateofOwnership
        ) {
          console.error(
            `❗ Owner data incomplete at index ${index}. Skipping.`
          );
          return Promise.resolve();
        }

        const documents = (owner.document || []).filter(
          (file) => file instanceof File
        );

        const dbId = owner.id;
        const removedDocIds = dbId ? docsToDelete[dbId] || [] : [];

        const payload = {
          member: owner.memberId,
          unit: parseInt(unitId, 10),
          ownershipPercentage: parseFloat(owner.ownershipPercentage),
          date_of_ownership: formatDate(owner.dateofOwnership), // Corrected format
          documents,
          ownership_transfer_from: owner.ownershipTransferFromId || null,
          docs_to_delete: removedDocIds // Always include
        };

        console.log(
          `📦 Payload for ${
            dbId ? "Update" : "Create"
          } Owner (index ${index}):`,
          payload
        );

        if (dbId) {
          return dispatch(updateOwner({ ownerId: dbId, payload })).unwrap();
        } else {
          return dispatch(createOwner(payload)).unwrap();
        }
      });

      await Promise.all(ownerPromises);
      console.log("✅ All owners submitted successfully.");

      await dispatch(fetchOwnerList(unitId));
      setDocsToDelete({}); // ✅ Clear after submission
    } catch (error) {
      console.error("🚨 Error submitting form:", error);
      setLocalError(error || "Failed to submit owner details.");
    }
  };

  return (
    <>
      <AddCompany
        isOpen={showCompanyModal}
        onClose={() => setShowCompanyModal(false)}
      />

      {(localError || ownerState.successMessage || ownerState.error) && (
        <MessageBox
          message={localError || ownerState.successMessage || ownerState.error}
          error={!!(localError || ownerState.error)}
          clearMessage={() => {
            if (localError) {
              setLocalError(null);
            } else {
              dispatch(clearMessage());
            }
          }}
          onOk={() => {
            if (localError) {
              setLocalError(null);
            } else {
              dispatch(clearMessage());
              if (ownerState.successMessage) {
                navigate(`/unit-details/${unitId}?tab=2`);
              }
            }
          }}
        />
      )}

      {showConfirmation && (
        <ConfirmationMessageBox
          message="Are you sure you want to delete this Owner?"
          onConfirm={handleConfirmDelete}
          onCancel={handleCancelDelete}
        />
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="p-5">
        <div className="flex flex-wrap gap-4 mb-6">
          <button
            type="button"
            className="bg-teal-600 hover:bg-teal-700 text-white font-medium px-5 py-2.5 rounded-lg shadow"
            onClick={() => setIsMemberModalOpen(true)}
          >
            + Add Member
          </button>
          <button
            type="button"
            className="bg-teal-600 hover:bg-teal-700 text-white font-medium px-5 py-2.5 rounded-lg shadow"
            onClick={() => setShowCompanyModal(true)}
          >
            + Add Company
          </button>
        </div>

        {fields.map((field, index) => {
          const filePath = `owners.${index}.document`;
          const files = watch(filePath) || [];
          const docLinks = watch(`owners.${index}.docLinks`) || [];
          ownerRefs.current[index] =
            ownerRefs.current[index] || React.createRef();

          return (
            <div
              ref={ownerRefs.current[index]}
              key={field.id}
              className="relative bg-white border rounded-2xl p-6 shadow-sm mb-6"
            >
              <div className="flex items-start justify-between">
                <h4 className="text-base font-medium mb-4 text-green">
                  {getOrdinal(index + 1)} Ownership Details
                </h4>
                <div className="flex gap-2">
                  {fields.length > 1 && (
                    <button
                      type="button"
                      onClick={() => handleRemoveOwner(index)}
                      className="w-10 h-10 text-xl text-teal-600 font-bold rounded-full border border-teal-600 hover:bg-teal-50 flex items-center justify-center transition"
                    >
                      <FaMinus size={12} />
                    </button>
                  )}
                  <button
                    type="button"
                    onClick={() =>
                      append({
                        memberId: "",
                        ownershipPercentage: "",
                        dateofOwnership: "",
                        document: [],
                        docLinks: [],
                        ownershipTransferFromId: ""
                      })
                    }
                    className="w-10 h-10 text-xl text-teal-600 font-bold rounded-full border border-teal-600 hover:bg-teal-50 flex items-center justify-center transition"
                  >
                    <FaPlus size={12} />
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-3 lg:w-[687px]">
                <div className="login-field">
                  <Paragraph className="my-3 text-sm font-medium">
                    Unit Owner Name*
                  </Paragraph>
                  <MemberSearchAutocomplete
                    value={searchTerms[index] || ""}
                    memberId={watch(`owners.${index}.memberId`)}
                    onSelect={(member) => {
                      const existingMemberIds = fields
                        .filter((_, i) => i !== index)
                        .map((_, i) => watch(`owners.${i}.memberId`))
                        .filter(Boolean);

                      if (existingMemberIds.includes(member.id)) {
                        setError(`owners.${index}.memberId`, {
                          type: "manual",
                          message:
                            "This member has already been added as an owner."
                        });
                      } else {
                        clearErrors(`owners.${index}.memberId`);
                        setValue(`owners.${index}.memberId`, member.id);
                        setSearchTerms((prev) => ({
                          ...prev,
                          [index]: member.full_name
                        }));
                      }
                    }}
                    disabled={field.isExisting}
                    excludedMemberIds={fields
                      .filter((_, i) => i !== index) // Exclude current field itself
                      .map((_, i) => watch(`owners.${i}.memberId`))
                      .filter(Boolean)} // Only non-empty ids
                  />

                  {errors.owners?.[index]?.memberId && (
                    <p className="text-red-500 text-sm">
                      {errors.owners[index].memberId.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 gap-3 lg:w-[687px]">
                <div className="login-field">
                  <Paragraph className="my-3 text-sm font-medium">
                    Ownership Transfer From
                  </Paragraph>
                  <MemberSearchAutocomplete
                    value={searchTerms[`from_${index}`] || ""}
                    memberId={watch(`owners.${index}.ownershipTransferFromId`)}
                    onSelect={(member) => {
                      setValue(
                        `owners.${index}.ownershipTransferFromId`,
                        member.id
                      );
                      setSearchTerms((prev) => ({
                        ...prev,
                        [`from_${index}`]: member.full_name
                      }));
                    }}
                  />

                  {errors.owners?.[index]?.memberId && (
                    <p className="text-red-500 text-sm">
                      {errors.owners[index].ownershipTransferFromId.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
                <div className="login-field">
                  <Paragraph className="my-3 text-sm font-medium">
                    Ownership Percentage*
                  </Paragraph>
                  <Controller
                    name={`owners.${index}.ownershipPercentage`}
                    control={control}
                    render={({ field }) => (
                      <NumberInputComponent
                        {...field}
                        placeholder="%"
                        step="0.01"
                      />
                    )}
                  />
                  {errors.owners?.[index]?.ownershipPercentage && (
                    <p className="text-red-500 text-sm">
                      {errors.owners[index].ownershipPercentage.message}
                    </p>
                  )}
                </div>

                <div className="login-field">
                  <Paragraph className="my-3 text-sm font-medium">
                    Date of Ownership*
                  </Paragraph>
                  <input
                    type="date"
                    {...register(`owners.${index}.dateofOwnership`)}
                    className="w-full border px-3 py-2 rounded"
                  />
                  {errors.owners?.[index]?.dateofOwnership && (
                    <p className="text-red-500 text-sm">
                      {errors.owners[index].dateofOwnership.message}
                    </p>
                  )}
                </div>

                <div className="login-field col-span-2">
                  <Paragraph className="my-3 text-sm font-medium">
                    Upload Ownership Documents
                  </Paragraph>

                  <FileDropzone
                    files={files}
                    docLinks={docLinks}
                    onDrop={(acceptedFiles) => {
                      const newFiles = [...files, ...acceptedFiles];
                      if (newFiles.length > 5) {
                        setError(`owners.${index}.document`, {
                          type: "manual",
                          message: "You can upload a maximum of 5 documents."
                        });
                        return;
                      }
                      setValue(filePath, newFiles);
                      clearErrors(`owners.${index}.document`);
                    }}
                    onRemove={(idx, type) => {
                      if (type === "file") {
                        const updatedFiles = files.filter((_, i) => i !== idx);
                        setValue(filePath, updatedFiles);
                        if (updatedFiles.length <= 5)
                          clearErrors(`owners.${index}.document`);
                      } else if (type === "docLink") {
                        const removedDoc = docLinks[idx];
                        const removedDocId = removedDoc?.id;
                        const updatedDocLinks = docLinks.filter(
                          (_, i) => i !== idx
                        );

                        setValue(`owners.${index}.docLinks`, updatedDocLinks);

                        const ownerDbId = watch(`owners.${index}.id`);

                        console.log("🗑 Removing docLink:", {
                          ownerDbId,
                          removedDocId,
                          removedDocUrl: removedDoc?.url
                        });

                        if (ownerDbId && removedDocId) {
                          setDocsToDelete((prev) => ({
                            ...prev,
                            [ownerDbId]: [
                              ...(prev[ownerDbId] || []),
                              removedDocId
                            ]
                          }));
                        }
                      }
                    }}
                  />

                  {/* Show validation error for this owner's document field */}
                  {errors.owners?.[index]?.document && (
                    <div className="text-red-500 text-sm mt-2">
                      {errors.owners[index].document.message}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}

        <div className="mt-6">
          <button
            type="submit"
            className="w-full bg-teal-600 text-white py-2 px-4 rounded"
            disabled={ownerState.loading}
          >
            {ownerState.loading ? "Saving..." : "Save"}
          </button>
        </div>
      </form>

      <AddMemberForm
        isOpen={isMemberModalOpen}
        onClose={() => setIsMemberModalOpen(false)}
        unitId={unitId}
      />
    </>
  );
};

export default ChangeOwnerForm;
