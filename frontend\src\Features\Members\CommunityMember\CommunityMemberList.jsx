import React, { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchCommMembers } from '../../../redux/slices/commMember/commMemberSlice';
import FilterSelect3 from 'Components/FilterSelect/FilterSelect3';
import SearchBar from 'Components/Search/SearchBar';
import CommunityMemberListTable from 'Components/Table/Member/CommunityMemberListTable';
import TableSkeleton from 'Components/Loaders/TableSkeleton';
import Heading from 'Components/HeadingComponent/Heading';
import { Div } from 'Components/Ui/Div';

const CommunityMemberList = () => {
  const dispatch = useDispatch();
  const { items: members, loading, error } = useSelector(s => s.commMember);
  // separate searchTerm (updates on every keystroke)
  const [searchTerm, setSearchTerm] = useState('');
  // debouncedSearch only updates 500ms after the last keystroke
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [allMembers, setAllMembers] = useState([]);
  const [filters, setFilters] = useState({
    type: [], tower: [], unit: [], status: []
  });

  // debounce effect: update debouncedSearch 500ms after user stops typing
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchTerm.trim());
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  // seed allMembers once when the fetch returns
  useEffect(() => {
    if (members.length > 0 && allMembers.length === 0) {
      setAllMembers(members);
    }
  }, [members, allMembers]);

  // fetch whenever any filter array OR the debounced search changes
  useEffect(() => {
    dispatch(fetchCommMembers({
      ...filters,
      // only send search if ≥ 3 chars
      ...(debouncedSearch.length >= 3 ? { search: debouncedSearch } : {})
    }));
  }, [
    dispatch,
    filters.type,
    filters.tower,
    filters.unit,
    filters.status,
    debouncedSearch
  ]);

  // options (memoized)
  const memberTypeOptions = useMemo(() => [
    { value: 'owner', label: 'Owner' },
    { value: 'resident', label: 'Resident' },
    { value: 'staff', label: 'Staff' }
  ], []);

  const towerOptions = useMemo(() =>
    Array.from(new Set(allMembers.map(m => m.tower)))
      .map(v => ({ value: v, label: v }))
  , [allMembers]);

  const unitOptions = useMemo(() =>
    Array.from(new Set(allMembers.map(m => m.unit)))
      .map(v => ({ value: v, label: v }))
  , [allMembers]);

  const statusOptions = useMemo(() => [
    { value: 'Active', label: 'Active' },
    { value: 'Inactive', label: 'Inactive' }
  ], []);

  return (
    <Div className="p-2">
      <Div className="container">
        <Div className="relative shadow rounded-27 bg-white py-4 px-4">
          <Div className="flex justify-between items-center py-4">
            <Heading title="Community Members List" size="lg" color="text-black" />
            <Div className="flex items-center space-x-4">
              <FilterSelect3
                placeholder="Member Type"
                options={memberTypeOptions}
                value={filters.type}
                onApply={val => setFilters(f => ({ ...f, type: val }))}
              />
              <FilterSelect3
                placeholder="Tower"
                options={towerOptions}
                value={filters.tower}
                onApply={val => setFilters(f => ({ ...f, tower: val }))}
              />
              <FilterSelect3
                placeholder="Unit"
                options={unitOptions}
                value={filters.unit}
                onApply={val => setFilters(f => ({ ...f, unit: val }))}
              />
              <FilterSelect3
                placeholder="Status"
                options={statusOptions}
                value={filters.status}
                onApply={val => setFilters(f => ({ ...f, status: val }))}
              />
              {/* SearchBar now only updates local searchTerm */}
              <SearchBar
                placeholder="Search name or email…"
                updateUrl={false}
                onSearch={text => setSearchTerm(text)}
              />
            </Div>
          </Div>

          {loading ? (
            <div className="flex items-center justify-center my-12">
              <TableSkeleton />
            </div>
          ) : members.length === 0 ? (
            <div className="text-center my-12 text-gray-500">
              No results found
            </div>
          ) : (
            <CommunityMemberListTable members={members} error={error} />
          )}
        </Div>
      </Div>
    </Div>
  );
};

export default CommunityMemberList;
