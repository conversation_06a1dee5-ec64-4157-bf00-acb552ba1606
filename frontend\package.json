{"name": "estate-link-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.7.9", "formik": "^2.4.6", "framer-motion": "^12.4.7", "lucide-react": "^0.483.0", "qs": "^6.14.0", "react": "^18.3.1", "react-datepicker": "^8.0.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}