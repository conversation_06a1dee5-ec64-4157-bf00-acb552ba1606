import React, { useState, useEffect } from "react";
import { FaSearch, FaTimes } from "react-icons/fa";
import axios from "axios";

const baseURL = import.meta.env.VITE_BASE_API || "http://127.0.0.1:8000";

const MemberSearchAutocomplete = ({ 
  value = "", 
  memberId = "", 
  onSelect, 
  unitId = null, 
  isOwnerSearch = false,
  disabled = false,
  readOnly = false,
  isDisabled = false,
  hideClearButton = false 
}) => {
  const [searchTerm, setSearchTerm] = useState(value);
  const [members, setMembers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedMember, setSelectedMember] = useState(
    memberId && value ? { id: memberId, full_name: value } : null
  );

  // Update input from parent on mount or prop change
  useEffect(() => {
    setSearchTerm(value);
    if (memberId && value) {
      setSelectedMember({ id: memberId, full_name: value });
    }
  }, [value, memberId]);

  useEffect(() => {
    if (searchTerm.trim().length < 3 || selectedMember || disabled || isDisabled) {
      setMembers([]);
      setError("");
      return;
    }

    const fetchMembers = async () => {
      setLoading(true);
      setError("");
      try {
        let url = `${baseURL}/towers/add_owner_search/?search=${encodeURIComponent(searchTerm)}`;
        if (isOwnerSearch && unitId) {
          url += `&unit_id=${unitId}`;
        }
        const res = await axios.get(url);
        setMembers(res.data?.member_data || []);
      } catch (err) {
        console.error("Failed to fetch members:", err);
        setMembers([]);
        setError("Failed to load members. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    const delay = setTimeout(fetchMembers, 300);
    return () => clearTimeout(delay);
  }, [searchTerm, selectedMember, isOwnerSearch, unitId, disabled, isDisabled]);

  const handleSelect = (member) => {
    if (disabled || isDisabled) return;
    setSelectedMember(member);
    setSearchTerm(member.full_name);
    setMembers([]);
    if (onSelect) onSelect(member);
  };

  const handleClear = () => {
    if (disabled || isDisabled) return;
    setSelectedMember(null);
    setSearchTerm("");
    setMembers([]);
    if (onSelect) onSelect({ id: "", full_name: "" });
  };

  return (
    <div className="bg-white border shadow rounded-xl p-4 relative">
      <div className="relative mb-4">
        <span className="absolute inset-y-0 left-0 flex items-center pl-3">
          <FaSearch className={`${disabled || isDisabled ? 'text-gray-400' : 'text-gray-500'}`} />
        </span>
        <input
          type="text"
          placeholder="Search members..."
          value={searchTerm}
          onChange={e => {
            if (disabled || isDisabled) return;
            setSearchTerm(e.target.value);
            setSelectedMember(null);
          }}
          className={`w-full pl-10 border border-gray-300 rounded-lg p-2 focus:outline-none ${
            disabled || isDisabled ? 'bg-gray-100 cursor-not-allowed' : ''
          }`}
          disabled={disabled || isDisabled || !!selectedMember}
          readOnly={readOnly}
        />
        {selectedMember && !disabled && !isDisabled && !hideClearButton && (
          <button
            type="button"
            className="absolute right-2 top-2 text-gray-400 hover:text-red-500"
            onClick={handleClear}
            title="Clear selection"
          >
            <FaTimes />
          </button>
        )}
      </div>

      {searchTerm.length >= 3 && !selectedMember && !disabled && !isDisabled && (
        <>
          <h2 className="text-lg font-semibold mb-2">Select Member</h2>
          <div className="overflow-x-auto max-h-60 overflow-y-auto border rounded">
            <table className="w-full table-auto border-collapse text-sm">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="px-4 py-2 text-left">Name</th>
                  <th className="px-4 py-2 text-left">Tower</th>
                  <th className="px-4 py-2 text-left">Unit</th>
                  <th className="px-4 py-2 text-left">Role</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan="4" className="text-center py-4">
                      Loading...
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan="4" className="text-center py-4 text-red-500">
                      {error}
                    </td>
                  </tr>
                ) : members.length === 0 ? (
                  <tr>
                    <td colSpan="4" className="text-center py-4 text-gray-500">
                      No members found.
                    </td>
                  </tr>
                ) : (
                  members
                    .map((member) => (
                      <tr
                        key={member.id}
                        className="hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleSelect(member)}
                      >
                        <td className="px-4 py-2">{member.full_name}</td>
                        <td className="px-4 py-2">{member.tower || "—"}</td>
                        <td className="px-4 py-2">{member.unit || "—"}</td>
                        <td className="px-4 py-2">{member.roles?.join(", ") || "—"}</td>
                      </tr>
                    ))
                )}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
};

export default MemberSearchAutocomplete;