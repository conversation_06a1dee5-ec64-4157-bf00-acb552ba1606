import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { createMember, memberUpdate} from "./../../../redux/slices/api/memberApi";
import { clearMessage } from "../../../redux/slices/memberSlice";
import { useNavigate } from "react-router-dom";

const useMemberSubmit = (formData, validateForm, activeTab) => {
  const dispatch = useDispatch();
  const { message, error } = useSelector((state) => state.member);
  const [showMessage, setShowMessage] = useState(false);
  const [loading, setLoading] = useState(false);

  // Show success or error message
//   useEffect(() => {
//     if (message || error) {
//       setShowMessage(true);
//       setTimeout(() => {
//         dispatch(clearMessage());
//         setShowMessage(false);
//         if (message) {
//           navigate("/member-list");
//         }
//       }, 4000);
//     }
//   }, [message, error, dispatch, navigate]);

  // const handleSubmit = (e) => {
  //     e.preventDefault();
  //     const validationPassed = validateForm(formData, 2);

  //     if (!validationPassed) {
  //         return;
  //     }

  //     const formDataObject = new FormData();
  //     for (const key in formData) {
  //         if (formData.hasOwnProperty(key) && key !== "members_role") {
  //             formDataObject.append(key, formData[key]);
  //         }
  //     }

  //     for (const key in formData) {
  //         if (formData.hasOwnProperty(key) && key !== "members_role") {
  //             if (key === "memberType") {
  //                 // Convert "memberType" to "member_type"
  //                 formDataObject.append("member_type", formData[key]);
  //             } else {
  //                 formDataObject.append(key, formData[key]);
  //             }
  //         }
  //     }

  //     let single_member_role = [];
  //     if (Array.isArray(formData.members_role)) {
  //         single_member_role = [...new Set(formData.members_role)].map((roleId) =>
  //             parseInt(roleId, 10)
  //         );
  //     }

  //     single_member_role.forEach((roleId) => {
  //         formDataObject.append("members_role", roleId);
  //     });

  //     // dispatch(createMember(formDataObject));
  //     setLoading(true);

  //     // Dispatch the createMember action and update loading accordingly
  //     dispatch(createMember(formDataObject))
  //       .then(() => {
  //         setLoading(false);
  //       })
  //       .catch(() => {
  //         setLoading(false);
  //       })
  // };

  const handleSubmit = (e) => {
    e.preventDefault();
    // console.log(formData);
    const validationPassed = validateForm(formData, activeTab);
    // console.log(validationPassed);
    // console.log('submit:',formData);


    if (!validationPassed) {
      return;
    }

    const formDataObject = new FormData();
    let memberTypeValue = formData.memberType || null; // Ensure we capture memberType

    for (const key in formData) {
      if (
        formData.hasOwnProperty(key) &&
        key !== "members_role" &&
        key !== "memberType"
      ) {
        formDataObject.append(key, formData[key]);
      }
    }
   console.log('object',formDataObject);
    // Ensure "member_type" is set if it exists
    if (memberTypeValue !== null) {
      formDataObject.append("member_type", memberTypeValue);
    }

    let single_member_role = [];
    if (Array.isArray(formData.members_role)) {
      single_member_role = [...new Set(formData.members_role)].map((roleId) =>
        parseInt(roleId, 10)
      );
    }

    single_member_role.forEach((roleId) => {
      formDataObject.append("members_role", roleId);
    });

  setLoading(true);

  const id=formDataObject.get('id');
  dispatch(id ? memberUpdate({id, formData: formDataObject}) : createMember(formDataObject))
      .then(() => {
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };


  useEffect(() => {
    if (message || error) {
      setShowMessage(true);  // MessageBox দেখানোর জন্য
    }
  }, [message, error]);



  // Optional: Reset message on dismiss
  const handleDismissMessage = () => {
    dispatch(clearMessage());
    setShowMessage(false);
  };

  return {
    handleSubmit,
    loading,
    showMessage,
    message,
    error,
    handleDismissMessage // Handle message box dismissal
  };
};

export default useMemberSubmit;
