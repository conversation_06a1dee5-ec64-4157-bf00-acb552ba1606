import React from "react";
import Info from "../../../Components/Ui/Info";
import FilePreviewWithDownload from "../../TowersAndUnits/UnitDetails/components/FilePreviewWithDownload";
import EditButton from "../../../Components/Buttons/EditButton";
import { Link, useNavigate } from "react-router-dom";
import DynamicEditLink from "./DynamicLinkEditButton";

const StaffDetailsView = ({ unit_staff }) => {
  console.log(unit_staff,"unit_staff")
 
  return (
    <div>
      <div className="border rounded-lg p-5 border-gray-200 my-4">
        <h2 className=" py-2 text-lg font-bold text-primary">
          Unit Staff Details
        </h2>

        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-800">
      
          <span className="px-1">    Unit </span>
            {unit_staff?.unit_name || "N/A"}
          </h2>
        <DynamicEditLink
                  basePath="/unit-staff-edit/:staffid/:staffstatus"
                  params={{
                    staffid: unit_staff?.id,
                    staffstatus: unit_staff?.unit_staff_status,
                    
                  }}
                />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-3">
            <Info label="Type">Unit Staff</Info>
          </div>
          <div className="space-y-3">

            <Info label="Status">
              {unit_staff?.unit_staff_status == true ? "Live-in" : "Part-time"}
            </Info>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StaffDetailsView;
