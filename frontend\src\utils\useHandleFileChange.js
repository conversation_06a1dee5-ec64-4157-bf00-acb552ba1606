
import { useState, useCallback } from "react";

const useHandleFileChange = (setFormData) => {
  const [nidFront, setNidFront] = useState(null);
  const [nidBack, setNidBack] = useState(null);

  const handleFileChange = useCallback((field, file) => {
    setFormData((prevState) => ({
      ...prevState,
      [field]: file,
    }));

    if (field === "nid_front") {
      setNidFront(file);
    } else if (field === "nid_back") {
      setNidBack(file);
    }
  }, [setFormData]);


  const handleFile3 = (field, file) => {
    console.log("File3 selected:", file);
    setFormData((prevState) => ({
        ...prevState,
        [field]: file,
    }));
};
  return { handleFileChange,handleFile3, nidFront, nidBack };
};

export default useHandleFileChange;
