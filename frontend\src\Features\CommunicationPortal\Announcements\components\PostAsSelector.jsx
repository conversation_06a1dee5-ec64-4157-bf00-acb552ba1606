import React from 'react';
import { Controller } from 'react-hook-form';

/**
 * PostAsSelector Component
 * Standalone component for post as radio button selection
 */
const PostAsSelector = ({ control, errors, onChange }) => {
  const options = [
    { value: 'Creator', label: 'Creator' },
    { value: 'Group', label: 'Group' },
    { value: 'Member', label: 'Member' }
  ];

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Post as <span className="text-[#3D9D9B]">*</span>
      </label>
      <Controller
        name="postAs"
        control={control}
        render={({ field: { onChange: fieldOnChange, value } }) => (
          <div className="flex items-center space-x-6">
            {options.map((option) => (
              <label key={option.value} className="flex items-center">
                <input
                  type="radio"
                  value={option.value}
                  checked={value === option.value}
                  onChange={(e) => {
                    fieldOnChange(e.target.value);
                    if (onChange) onChange(e.target.value);
                  }}
                  className="w-4 h-4 text-[#3D9D9B] border-gray-300 focus:ring-[#3D9D9B] accent-[#3D9D9B]"
                />
                <span className="ml-2 text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        )}
      />
      {errors.postAs && (
        <p className="mt-1 text-sm text-red-600">{errors.postAs.message}</p>
      )}
    </div>
  );
};

export default PostAsSelector;
