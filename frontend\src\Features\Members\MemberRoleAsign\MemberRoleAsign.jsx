import React, { useState } from 'react';
import NavigateButton from '../../../Components/FormComponent/ButtonComponent/NavigateButton';
import { FaPlus } from 'react-icons/fa';
import AddRoleModal from '../AddRoleModal';


const MemberRoleAsign = ({
  label = 'Select Member Type',
  data,
  optionKey = 'name',
  valueKey = 'value',
  onChange,
  selectedOptions = [],
  isMultiSelect = true,
  onRoleCreated,
  onAddRoleClick
}) => {
  const [selectedValues, setSelectedValues] = useState(selectedOptions);
  // const [isAddRoleModalOpen, setIsAddRoleModalOpen] = useState(false);

  const handleSelectionChange = (option) => {
    let updatedSelection = [...selectedValues];
    if (isMultiSelect) {
      if (updatedSelection.includes(option)) {
        updatedSelection = updatedSelection.filter((item) => item !== option);
      } else {
        updatedSelection.push(option);
      }
    } else {
      updatedSelection = [option];
    }
    setSelectedValues(updatedSelection);
    if (onChange) onChange(updatedSelection);
  };

  const handleSelectAllChange = (e) => {
    const checked = e.target.checked;
    const allValues = checked ? data.map((option) => option[valueKey]) : [];
    setSelectedValues(allValues);
    if (onChange) onChange(allValues);
  };

  // Wrap the role-created callback: update parent's list and close the modal.
  // const handleRoleCreatedWrapper = (newRole) => {
  //   if (onRoleCreated) onRoleCreated(newRole);
  //   setIsAddRoleModalOpen(false);
  // };

  return (
    <div className="m-3">
      <div className="flex justify-between mb-2">
        <p className="text-base font-medium text-primary">{label}</p>
        <NavigateButton
          size="small"
          icon={FaPlus}
          className="bg-primary text-white"
          // onClick={() =>{ alert() ;setIsAddRoleModalOpen(true)}}
            onClick={onAddRoleClick}
        >
          Add New Role
        </NavigateButton>
      </div>
      {data.length === 0 ? (
        <div className="px-3 py-2">No options available</div>
      ) : (
        <div className="max-w-full h-[150px] overflow-x-auto bg-white border border-gray-300 rounded shadow-sm">
          {/* Select All Checkbox */}
          <div className="flex items-center px-3 py-2">
            <input
              type="checkbox"
              id="selectAll"
              checked={selectedValues.length === data.length}
              onChange={handleSelectAllChange}
              className="mr-2 accent-primary w-6 h-6"
              name="members_role"
            />
            <label
              htmlFor="selectAll"
              className="text-sm text-black cursor-pointer"
            >
              Select All
            </label>
          </div>
          {data.map((option, index) => (
            <label
              key={index}
              className="flex items-center px-3 py-2 cursor-pointer"
            >
              <input
                type={isMultiSelect ? 'checkbox' : 'radio'}
                checked={selectedValues.includes(option[valueKey])}
                onChange={() => handleSelectionChange(option[valueKey])}
                className="mr-2 accent-primary w-6 h-6"
                name="members_role"
              />
              <span className="text-sm text-black">
                {option[optionKey]}
              </span>
            </label>
          ))}
        </div>
      )}
      
      {/* Reusable modal for adding a new role */}
      {/* <AddRoleModal
        isOpen={isAddRoleModalOpen}
        onClose={() => setIsAddRoleModalOpen(false)}
        onRoleCreated={handleRoleCreatedWrapper}
      /> */}
    </div>
  );
};

export default MemberRoleAsign;
