import React from 'react';
import "./FormComponent.css"; 

function NumberInputComponent({ value, onChange, name, label, placeholder, width, error='', disabled = false, readOnly = false }, ref) {
  return (
    <div className="login-field">
      <input
        ref={ref}
        type="number"
        name={name} 
        value={value}
        className="login-field-input custom-input"
        style={{ width }}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        readOnly={readOnly}
      />
      <label className="login-field-label" htmlFor={label}>
        {label}
      </label>
      {error && <p className="text-red-500 text-xs">{error}</p>}
    </div>
  );
}

const ForwardedNumberInputComponent = React.forwardRef(NumberInputComponent);
ForwardedNumberInputComponent.displayName = 'NumberInputComponent';

export default ForwardedNumberInputComponent;
