import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { BiArrowBack } from "react-icons/bi";
import UnitTowerInfo from "../../UnitDetails/components/UnitTowerInfo";
import { useDispatch } from "react-redux";
import { clearMessage } from "../../../../redux/slices/owner/ownerSlice";
import ChangeOwnerForm from "./ChangeOwnerForm";
import { setActiveTabs } from "../../../../redux/slices/memberSlice";

const ChangeOwner = () => {
  const { unitId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    // Clear messages when component unmounts
    return () => {
      dispatch(clearMessage());
    };
  }, [dispatch]);

  return (
    <div className="h-full p-[14px]">
      <div className="container mx-auto">
        <div className="flex justify-between max-w-[1282px] py-4">
          <p className="flex items-center text-2xl font-medium">
            <BiArrowBack
              className="mr-2 text-gray-600 cursor-pointer"
              onClick={() => {
                dispatch(setActiveTabs(3));
                navigate(-1);
              }}
            />
            Change Ownership
          </p>
        </div>

        <div className="flex flex-col md:flex-row rounded-xl max-w-[1282px] bg-white">
          <UnitTowerInfo id={unitId} />
          <ChangeOwnerForm unitId={unitId} />
        </div>
      </div>
    </div>
  );
};

export default ChangeOwner;
