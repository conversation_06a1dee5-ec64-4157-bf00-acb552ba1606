import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useLocation, useNavigate } from "react-router-dom";
import { FaPlus } from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import { fetchRoles } from "../../../redux/slices/roles/rolesSlice";
import MemberListTable from "Components/Table/Member/MemberListTable";
import { Div } from "Components/Ui/Div";
import Image from "Components/Ui/Image";
import SearchBar from "Components/Search/SearchBar";
import Heading from "Components/HeadingComponent/Heading";
import Button from "../../../Components/FormComponent/ButtonComponent/Button";
import FilterSelect2 from "../../../Components/FilterSelect/FilterSelect2";
import TableSkeleton from "../../../Components/Loaders/TableSkeleton";
import useMemberList from "../useMemberList";
import { fetchMemberTypes } from "../../../redux/slices/api/memberApi";
import { checkPermission } from "../../../utils/permissionUtils"; 

const MemberList = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const [error, setError] = useState(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  const {
    members,
    loading: membersLoading,
    error: membersError
  } = useMemberList();

  const {
    memberTypes = [],
    loading: memberTypeLoading,
    error: memberTypeError
  } = useSelector((state) => state.member);

  useEffect(() => {
    dispatch(fetchMemberTypes());
  }, [dispatch]);

  useEffect(() => {
    setError(null);
  }, [location.pathname]);

  // Permission check
  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 3); 
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  const memberTypeOptions = memberTypes.map((item) => ({
    value: item.id,
    label: item.type_name
  }));
  if (loadingPermission) {
    return <div></div>;  
  }

  if (!hasPermission) {
    return <div>You are not authorized to view this page.</div>; 
  }

  return (
    <Div className="p-2">
      <Div className="container">
        <Div className="relative shadow rounded-27 bg-white py-4 px-4">
          <Div className="pb-4">
            <Div className="flex justify-between items-center py-4">
              <Heading title="Members List" size="xl" color="text-black" />
              <Div className="flex items-center space-x-4">
                <FilterSelect2
                  placeholder="Member Type"
                  options={memberTypeOptions}
                  paramKey="member_type"
                  onApply={(filters) => {
                    if (!filters) {
                      console.error("Filters object is undefined");
                      return;
                    }
                    console.log("Applied Filters:", filters);
                  }}
                />
                <SearchBar />
                <Div className="flex items-center">
                  <Image
                    src="./filter-icon/filter-3-line.png"
                    alt="Filter Icon"
                    className="rounded-lg"
                  />
                </Div>
                <Button
                  icon={FaPlus}
                  onClick={() => {
                    navigate("/create-member", {
                      state: { showMessage: false }
                    });
                  }}
                >
                  Add Member
                </Button>
              </Div>
            </Div>
          </Div>

          {/* Loading and Error Handling */}
          {membersLoading || memberTypeLoading ? (
            <div className="flex items-center justify-center my-12">
              <TableSkeleton />
            </div>
          ) : members.length === 0 ? (
            <div className="text-center my-12 text-gray-500">
              No results found
            </div>
          ) : (
            <MemberListTable member={members} error={membersError} />
          )}
        </Div>
      </Div>
    </Div>
  );
};

MemberList.propTypes = {
  members: PropTypes.array,
  memberTypes: PropTypes.array
};

MemberList.defaultProps = {
  members: [],
  memberTypes: []
};

export default MemberList;
