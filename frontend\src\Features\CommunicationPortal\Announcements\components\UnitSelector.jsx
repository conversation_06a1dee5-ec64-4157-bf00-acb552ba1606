import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, X } from 'lucide-react';
import { unitApi } from '../../../../api/announcementsApi/announcementsBackendApi';

const UnitSelector = ({ value, onChange, selectedTowers = [], placeholder = "Select Units" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState('bottom');
  const [units, setUnits] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const selectorRef = useRef(null);
  const inputRef = useRef(null);
  const isInitialMount = useRef(true);

  // Debug logging (can be removed in production)
  // console.log('UnitSelector received value:', value);
  // console.log('UnitSelector received selectedTowers:', selectedTowers);

  // Fetch units from backend based on selected towers
  useEffect(() => {
    const fetchUnits = async () => {
      if (!selectedTowers || selectedTowers.length === 0) {
        setUnits([]);
        // Only clear unit selection if it's not the initial mount
        if (!isInitialMount.current) {
          onChange([]);
        }
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Filter out 'All' from selectedTowers and get actual tower IDs
        const towerIds = selectedTowers.filter(id => id !== 'All');

        if (selectedTowers.includes('All') || towerIds.length === 0) {
          // If 'All' is selected, fetch all units
          const unitsData = await unitApi.getUnits();
          setUnits(unitsData);
        } else {
          // Fetch units for specific towers
          const unitsData = await unitApi.getUnits(towerIds);
          setUnits(unitsData);
        }
      } catch (err) {
        console.error('Error fetching units:', err);
        setError('Failed to load units');
        setUnits([]);
        // Only clear unit selection if it's not the initial mount
        if (!isInitialMount.current) {
          onChange([]);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUnits();
  }, [selectedTowers]); // Remove onChange from dependencies

  // Create unit options with 'All' option
  const availableUnits = units.length > 0 ? ['All', ...units.map(unit => unit.id)] : [];
  const regularUnits = units.map(unit => unit.id);

  // Helper function to get unit name by ID
  const getUnitName = (unitId) => {
    if (unitId === 'All') return 'All Units';
    const unit = units.find(u => u.id === unitId);
    return unit ? unit.unit_name : `Unit ${unitId}`;
  };

  // Calculate dropdown position
  const calculatePosition = () => {
    if (inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = 300;

      setDropdownPosition(spaceBelow < dropdownHeight && spaceAbove > spaceBelow ? 'top' : 'bottom');
    }
  };

  useEffect(() => {
    if (isOpen) {
      calculatePosition();
      window.addEventListener('scroll', calculatePosition);
      window.addEventListener('resize', calculatePosition);

      return () => {
        window.removeEventListener('scroll', calculatePosition);
        window.removeEventListener('resize', calculatePosition);
      };
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Clear units when towers change and selected units are no longer available
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    if (value && value.length > 0 && units.length > 0 && !loading) {
      const currentValues = value || [];
      const validUnits = currentValues.filter(unit =>
        unit === 'All' || units.some(u => u.id === unit)
      );

      // If some units are no longer valid, update the selection
      if (validUnits.length !== currentValues.length) {
        onChange(validUnits);
      }
    }
  }, [selectedTowers, units, value, loading]); // Remove onChange from dependencies

  const handleUnitChange = (unit, isChecked) => {
    const currentValues = value || [];

    if (unit === 'All') {
      if (isChecked) {
        onChange(units.map(u => u.id));
      } else {
        onChange([]);
      }
    } else {
      let newValues;
      if (isChecked) {
        newValues = [...currentValues, unit];
        // Check if all units are selected
        if (units.every(u => newValues.includes(u.id))) {
          newValues = [...newValues, 'All'];
        }
      } else {
        newValues = currentValues.filter(v => v !== unit && v !== 'All');
      }
      onChange(newValues);
    }
  };

  const handleClearAll = () => {
    onChange([]);
  };

  const getDisplayText = () => {
    if (!selectedTowers || selectedTowers.length === 0) {
      return "Select towers first";
    }
    if (!value || value.length === 0) {
      return placeholder;
    }
    if (value.includes('All')) {
      return 'All Units Selected';
    }
    return `${value.length} Unit(s) Selected`;
  };

  const getSelectedDisplay = () => {
    if (!value || value.length === 0) {
      return null;
    }

    if (value.includes('All')) {
      return (
        <div className="mb-2 p-2 bg-[#3D9D9B] bg-opacity-10 rounded-md border border-[#3D9D9B] border-opacity-30">
          <div className="text-sm font-medium text-[#3D9D9B] mb-1">Selected Units:</div>
          <div className="text-sm text-gray-700">All Units</div>
        </div>
      );
    }

    return (
      <div className="mb-2 p-2 bg-[#3D9D9B] bg-opacity-10 rounded-md border border-[#3D9D9B] border-opacity-30">
        <div className="text-sm font-medium text-[#3D9D9B] mb-1">Selected Units:</div>
        <div className="flex flex-wrap gap-1">
          {value.filter(unit => unit !== 'All').map((unitId) => (
            <span
              key={unitId}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[#3D9D9B] text-white"
            >
              {getUnitName(unitId)}
            </span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="relative" ref={selectorRef}>
      {/* Selected Values Display */}
      {getSelectedDisplay()}

      {/* Input Field */}
      <div
        ref={inputRef}
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent"
      >
        <span className={value && value.length > 0 ? 'text-gray-900 text-sm' : 'text-gray-500 text-xs'}>
          {getDisplayText()}
        </span>
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>

      {/* Dropdown content */}
      {isOpen && (
        <div className={`absolute z-50 ${
          dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'
        } w-full bg-white border border-gray-300 rounded-md shadow-lg`}>
          <div className="max-h-60 overflow-y-auto p-3">
            {loading ? (
              <div className="p-2 text-gray-500 text-sm text-center">Loading units...</div>
            ) : error ? (
              <div className="p-2 text-red-500 text-sm text-center">{error}</div>
            ) : !selectedTowers || selectedTowers.length === 0 ? (
              <div className="p-2 text-gray-500 text-sm text-center">
                Please select towers first to see available units
              </div>
            ) : availableUnits.length === 0 ? (
              <div className="p-2 text-gray-500 text-sm text-center">No units available</div>
            ) : (
              availableUnits.map((unit) => (
                <label key={unit} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={value?.includes(unit) || false}
                      onChange={(e) => handleUnitChange(unit, e.target.checked)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 border-2 rounded ${
                      value?.includes(unit)
                        ? 'bg-[#3D9D9B] border-[#3D9D9B]'
                        : 'border-gray-300 bg-white'
                    } flex items-center justify-center`}>
                      {value?.includes(unit) && (
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                    </div>
                  </div>
                  <span className="text-sm text-gray-700">{getUnitName(unit)}</span>
                </label>
              ))
            )}
          </div>
          <div className="border-t border-gray-200 p-3">
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleClearAll}
                className="flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors text-sm font-medium"
                title="Clear All"
              >
                <X className="w-4 h-4" />
              </button>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="flex-1 bg-[#3D9D9B] text-white py-2 px-4 rounded-md hover:bg-[#34877A] transition-colors text-sm font-medium"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(UnitSelector);
