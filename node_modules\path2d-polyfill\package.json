{"name": "path2d-polyfill", "version": "2.0.1", "description": "Polyfills Path2D api for canvas rendering", "scripts": {"build": "rm -rf dist/* && rollup -c", "start": "rollup -c -w", "lint": "eslint .", "check-types": "tsc --noEmit", "test": "ts-mocha test/*.spec.ts", "test:watch": "ts-mocha --watch test/*.spec.ts", "test:coverage": "nyc --reporter=html --reporter=text --reporter=text-summary yarn test", "format:check": "prettier --check \"./**\"", "format:write": "prettier --write \"./**\"", "prepare": "husky install", "release": "release-it"}, "main": "dist/path2d-node.cjs", "browser": "dist/path2d-polyfill.min.js", "exports": {"node": {"import": "./dist/path2d-node.mjs", "require": "./dist/path2d-node.cjs"}, "development": "./dist/path2d-polyfill.dev.js", "module": "./dist/path2d-polyfill.esm.js", "default": "./dist/path2d-polyfill.min.js"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/nilzona/path2d-polyfill.git"}, "keywords": ["Path2D", "polyfill", "canvas", "roundRect"], "author": "nilzona", "license": "MIT", "bugs": {"url": "https://github.com/nilzona/path2d-polyfill/issues"}, "homepage": "https://github.com/nilzona/path2d-polyfill#readme", "devDependencies": {"@commitlint/cli": "17.3.0", "@commitlint/config-conventional": "17.3.0", "@release-it/conventional-changelog": "5.1.1", "@rollup/plugin-terser": "0.2.1", "@rollup/plugin-typescript": "10.0.1", "@types/chai": "^4.3.4", "@types/mocha": "^10.0.1", "@types/sinon": "^10.0.13", "@types/sinon-chai": "^3.2.9", "@typescript-eslint/eslint-plugin": "5.48.0", "@typescript-eslint/parser": "5.48.0", "chai": "4.3.7", "eslint": "8.31.0", "eslint-config-prettier": "8.6.0", "husky": "8.0.2", "lint-staged": "13.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "prettier": "2.8.1", "release-it": "15.6.0", "rollup": "3.9.1", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-serve": "2.0.2", "sinon": "15.0.1", "sinon-chai": "3.7.0", "ts-mocha": "^10.0.0", "typescript": "^4.9.4"}, "dependencies": {}, "engines": {"node": ">=8"}}