import React from "react";
import Info from "../../../Components/Ui/Info";
import FilePreviewWithDownload from "../../TowersAndUnits/UnitDetails/components/FilePreviewWithDownload";
import EditButton from "../../../Components/Buttons/EditButton";
import { Link, useNavigate } from "react-router-dom";
import DynamicEditLink from "./DynamicLinkEditButton";
// import FilePreviewWithDownload from "../../UnitDetails/components/FilePreviewWithDownload";

const ResidentDetailsView = ({ residentData }) => {
  const navigate = useNavigate();

  console.log("residentData?.idresidentData?.id", residentData);
  return (
    <div className="border rounded-lg p-5 border-gray-200 my-4">
            <h2 className=" py-2 text-lg font-bold text-primary">Resident Details</h2>

      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-800">
        <span className="px-1">    Unit </span>
          {residentData?.unit_name || "N/A"}
        </h2>
        <DynamicEditLink
          basePath="/resident_info_edit/:unitid/:residentid"
          params={{
            unitid: residentData?.unit,
            residentid: residentData?.id
          }}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-3">
          <Info label="Member Type">
            {residentData?.is_resident_or_tenant == 0 ? "Tenant " : "Owner"}
          </Info>
          <Info label="Unit Name">{residentData?.unit_name || "N/A"}</Info>
          <Info label="Unit Rent Fee">
            {residentData?.unit_rent_fee
              ? `${residentData.unit_rent_fee}`
              : "N/A"}
          </Info>
          <Info label="Notice Period">
            {residentData?.notice_period
              ? `${residentData.notice_period} month(s)`
              : "N/A"}
          </Info>
        </div>

        <div className="space-y-3">
          <Info label="Tower Name">
            {residentData?.tower?.tower_name || "N/A"}
          </Info>
          <Info label="Tower Number">
            {residentData?.tower?.tower_number || "N/A"}
          </Info>
          <Info label="Advance Payment">
            {residentData?.advance_payment
              ? `${residentData.advance_payment}`
              : "N/A"}
          </Info>
        </div>

        <div className="space-y-3">
          <p className="text-gray-500 text-sm">Resident Docs</p>
          <div className="flex flex-row gap-2 overflow-x-auto ">
            {residentData?.resident_docs?.length > 0 ? (
              residentData.resident_docs.map((doc, index) => (
                <FilePreviewWithDownload
                  key={index}
                  filePath={doc.rental_docs}
                  fileName={`Document_${index + 1}`}
                />
              ))
            ) : (
              <p className="text-sm text-gray-500">No documents available</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResidentDetailsView;
