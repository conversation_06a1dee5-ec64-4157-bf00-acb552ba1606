// src/pages/roles/AddRole.jsx
import React, { useState, useEffect } from "react";
import { BiArrowBack } from "react-icons/bi";
import { Link, useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import TextInputComponent from "../../../Components/FormComponent/TextInputComponent";
import TextareaComponent from "../../../Components/FormComponent/TextareaComponent";
import {
  fetchPermissions,
  createRole,
  updateRole,
  clearMessages,
  fetchRoleDetails
} from "../../../redux/slices/roles/rolesSlice";
import SubmitButton from "../../../Components/FormComponent/ButtonComponent/SubmitButton";
import MessageBox from "../../../Components/MessageBox/MessageBox";
import ConfirmationMessageBox from "../../../Components/MessageBox/ConfirmationMessageBox";
import ErrorMessage from "../../../Components/MessageBox/ErrorMessage";
import { checkPermission } from "../../../utils/permissionUtils";

const AddRole = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    permissions,
    loading,
    error: backendError,
    successMessage,
    roleDetails
  } = useSelector((state) => state.role);
  const editMode = Boolean(id);

  const [formData, setFormData] = useState({
    role_name: "",
    role_description: "",
    permissions: []
  });
  const [errors, setErrors] = useState({ role_name: "", });
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);

  useEffect(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionId = editMode ? 5 : 4;
      const permissionGranted = await checkPermission("org", permissionId);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
    dispatch(fetchPermissions());
    if (editMode) dispatch(fetchRoleDetails(id));
  }, [dispatch, editMode, id]);

  useEffect(() => {
    if (editMode && roleDetails) {
      setFormData({
        role_name: roleDetails.role_name || "",
        role_description: roleDetails.role_description || "",
        permissions: roleDetails.selected_permissions || []
      });
    }
  }, [editMode, roleDetails]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));

    if (name === "role_description" && value.length > 255) {
    setErrors((prev) => ({
      ...prev,
      role_description: "Role description cannot exceed 255 characters"
    }));
  };
  }
  const handleCheckboxChange = (e, permissionId) => {
    setFormData((prev) => ({
      ...prev,
      permissions: e.target.checked
        ? [...prev.permissions, permissionId]
        : prev.permissions.filter((id) => id !== permissionId)
    }));
  };

  const handleSelectAllChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      permissions: e.target.checked ? permissions.map((p) => p.id) : []
    }));
  };

  const handleUpdateConfirmed = () => {
    setShowConfirmation(false);
    dispatch(updateRole({ id, roleData: formData }))
      .unwrap()
      .catch((err) => {
        setErrors({
          role_name: err.role_name ? err.role_name[0] : "",
          role_description: err.role_description ? err.role_description[0] : ""
        });
      });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // 1) Clear previous errors
    setErrors({ role_name: "", role_description: "" });

    // 2) Client-side validation
    const validationErrors = {};
    if (!formData.role_name.trim()) {
      validationErrors.role_name = "Role name is required";
    }
    if (formData.role_description.length > 255) {
    validationErrors.role_description = "Role description cannot exceed 255 characters";
  }
 
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return; // stop here
    }

    if (editMode) {
      const noChanges =
        formData.role_name === roleDetails?.role_name &&
        formData.role_description === roleDetails?.role_description &&
        JSON.stringify([...formData.permissions].sort()) ===
          JSON.stringify([...(roleDetails?.selected_permissions || [])].sort());
      if (noChanges) return;
      setShowConfirmation(true);
    } else {
      dispatch(createRole(formData))
        .unwrap()
        .catch((err) => {
          if (err.role_name || err.role_description) {
            console.log("Backend Error:", err);
          }
        });
    }
  };

  const handleClearMessage = () => {
    dispatch(clearMessages());
    navigate(editMode ? `/roleProfile/${id}` : "/roleList");
  };

  if (loadingPermission) return null;
  if (!hasPermission) navigate("/not-authorized");

  return (
    <div className="h-full p-[14px]">
      <div className="container">
        <div className="flex justify-between py-[14px] items-center">
          <Link to={editMode ? `/roleProfile/${id}` : "/roleList"}>
            <p className="flex items-center font-medium text-[24px]">
              <BiArrowBack className="mr-2 text-gray-600" />
              {editMode ? "Edit Role" : "Add Role"}
            </p>
          </Link>
        </div>
        <form
          onSubmit={handleSubmit}
          className="bg-white border p-[14px] rounded-[27px] mx-auto"
        >
          <div className="mb-4">
            <SubmitButton
              text={editMode ? "Update" : "Create"}
              loading={loading}
              disabled={loading}
              onClick={handleSubmit}
            />
          </div>

          {/* Role Name */}
          <TextInputComponent
            name="role_name"
            label="Role Name"
            placeholder="Write Role Name"
            value={formData.role_name}
            onChange={handleChange}
          />
          {errors.role_name && <ErrorMessage message={errors.role_name} />}

          {/* Role Description */}
          <div className="mt-4">
            <p className="mb-2 text-sm font-semibold">Role Description</p>
            <TextareaComponent
              name="role_description"
              value={formData.role_description}
              onChange={handleChange}
              placeholder="Write Role Description"
              rows={5}
            />
                      {errors.role_description && <ErrorMessage message={errors.role_description} />}

          </div>

          {/* Permissions */}
          <div className="mt-6">
            <h2 className="text-[20px] font-semibold mb-4">Role Permissions</h2>
            <div className="grid grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-[10px] shadow">
                <div className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4"
                    onChange={handleSelectAllChange}
                    checked={
                      permissions.length > 0 &&
                      formData.permissions.length === permissions.length
                    }
                  />
                  <label className="ml-2">Select All</label>
                </div>
                {permissions.map((perm) => (
                  <div key={perm.id} className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4"
                      onChange={(e) => handleCheckboxChange(e, perm.id)}
                      checked={formData.permissions.includes(perm.id)}
                    />
                    <label className="ml-2">{perm.permission_name}</label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </form>

        {/* Backend Error MessageBox */}
        {backendError && (
          <MessageBox
            type="error"
            error
            message={
              backendError.role_name?.[0] ||
              backendError.detail ||
              "An error occurred."
            }
            clearMessage={() => dispatch(clearMessages())}
            onOk={() => dispatch(clearMessages())}
          />
        )}

        {/* Success MessageBox */}
        {successMessage && (
          <MessageBox
            message={successMessage}
            clearMessage={handleClearMessage}
            onOk={handleClearMessage}
          />
        )}

        {/* Confirmation Modal */}
        {showConfirmation && (
          <ConfirmationMessageBox
            message="Are you sure you want to update this role?"
            onConfirm={handleUpdateConfirmed}
            onCancel={() => setShowConfirmation(false)}
          />
        )}
      </div>
    </div>
  );
};

export default AddRole;
