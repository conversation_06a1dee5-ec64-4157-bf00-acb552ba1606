import React, { useEffect, useState } from "react";
import { <PERSON>a<PERSON><PERSON>, FaPlus } from "react-icons/fa";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { fetchRoles } from "../../../redux/slices/roles/rolesSlice";
import FilterSelect1 from "../../../Components/FilterSelect1/FilterSelect1";
import SearchBar from "../../../Components/Search/SearchBar";
import Status from "../../Text/Status";
import Button from "../../FormComponent/ButtonComponent/Button";
import Heading from "Components/HeadingComponent/Heading";

// Sample status options for FilterSelect1
const statusOptions = [
  { label: "Active", value: "1" },
  { label: "Inactive", value: "0" }
];

const RoleListTable = ({ addRole, roleProfile }) => {
  const dispatch = useDispatch();
  const { roles, loading, error } = useSelector((state) => state.role);
  const navigate = useNavigate();
  // Local states for filters
  const [search, setSearch] = useState("");
  const [appliedSearch, setAppliedSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState([]); // expects an array of "1" or "0"

  // Called when the search button in SearchBar is clicked.
  const handleSearch = (value) => {
    // Only update the applied search if the term is empty or has at least 3 characters.
    if (value.length === 0 || value.length >= 3) {
      setAppliedSearch(value);
    }
  };

  // Fetch roles whenever the status filter or applied search term changes.
  useEffect(() => {
    const filters = {};
    if (statusFilter && statusFilter.length > 0) {
      filters.status = statusFilter;
    }
    if (appliedSearch) {
      filters.search = appliedSearch;
    }
    dispatch(fetchRoles(filters));
  }, [dispatch, statusFilter, appliedSearch]);

  return (
    <div className="relative overflow-x-auto bg-white rounded-27 py-5 px-5">
      <div className="pb-4">
        <div className="flex justify-between items-center py-4">
          <div>
            {/* <p className="text-xl font-semibold">Role List</p> */}
            <Heading title="Role List" size="xl" color="text-black" />
          </div>
          <div className="flex items-center space-x-4">
            <FilterSelect1
              placeholder="Status"
              options={statusOptions}
              paramKey="status"
              onApply={(selected) => setStatusFilter(selected)}
            />
            <SearchBar
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              onSearch={handleSearch}
              placeholder="Search list..."
              updateUrl={false}
            />
            {/* <Link to={`/${addRole}`}>
              <Button
                size="small"
                className="flex items-center justify-between text-base font-medium bg-[#3D9D9B] text-white hover:bg-[#34977A] transition-all duration-300 px-4 py-3 rounded"
              >
                <FaPlus className="mr-2" />
                Add Role
              </Button>
            </Link> */}
            <Button
              icon={FaPlus}
              onClick={() => {
                navigate("/addRole");
              }}
            >
              Add Role
            </Button>
          </div>
        </div>
      </div>

      {loading ? (
        <p>Loading roles...</p>
      ) : error ? (
        <p>Error: {error}</p>
      ) : (
        <div className="w-full max-h-[70vh] overflow-auto border rounded-lg bg-white">
          <table className="min-w-[900px] w-full text-base text-left">
            <thead className="bg-primary shadow-lg border-b border-primary text-white sticky top-0 z-10">
              <tr>
                <th className="px-3 py-3 text-base font-bold ">Role Name</th>
                <th className="px-3 py-3 text-base font-bold ">
                  Role Description
                </th>
                <th className="px-3 py-3 text-base font-bold ">Permissions</th>
                <th className="px-3 py-3 text-base font-bold ">Status</th>
                <th className="px-3 py-3 text-base font-bold  ">Action</th>
              </tr>
            </thead>
            <tbody>
              {roles && roles.length > 0 ? (
                roles.map((role) => (
                  <tr
                    key={role.id}
                    className="bg-white border-b hover:bg-gray-50 transition-colors duration-150"
                  >
                    <td className="px-3 py-3 text-base whitespace-nowrap">
                      {typeof role.role_name === "object"
                        ? role.role_name.role_name
                        : role.role_name}
                    </td>
                    <td className="px-3 py-3 text-base whitespace-nowrap">
                      {role.role_description}
                    </td>
                    <td
                      className="px-3 py-3 text-base whitespace-nowrap"
                      title={role.permissions_list?.join(", ")}
                    >
                      {role.permissions_list && role.permissions_list.length > 3
                        ? `${role.permissions_list.slice(0, 3).join(", ")}, ...`
                        : role.permissions_list?.join(", ")}
                    </td>

                    <td className="mx-2 font-[600] py-1 px-2 rounded-8 text-white">
                      {role.is_active ? (
                        <Status color="primary" text="Active" />
                      ) : (
                        <Status color="red" text="Inactive" />
                      )}
                    </td>
                    <td className="px-3 py-3 text-center whitespace-nowrap">
                      <Link to={`/${roleProfile}/${role.id}`}>
                        <FaEye className="w-[25px] h-[20px] cursor-pointer text-primary" />
                      </Link>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan="5"
                    className="px-3 py-6 text-center text-gray-500"
                  >
                    No result found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default RoleListTable;
