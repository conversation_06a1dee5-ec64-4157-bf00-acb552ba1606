!function(){"use strict";function t(t,e,n){if(n||2===arguments.length)for(var a,o=0,r=e.length;o<r;o++)!a&&o in e||(a||(a=Array.prototype.slice.call(e,0,o)),a[o]=e[o]);return t.concat(a||Array.prototype.slice.call(e))}var e={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},n=/([astvzqmhlc])([^astvzqmhlc]*)/gi,a=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/gi;function o(o){var r=[],s=String(o).trim();return"M"!==s[0]&&"m"!==s[0]||s.replace(n,(function(n,o,s){var c=function(t){var e=t.match(a);return e?e.map(Number):[]}(s),i=o.toLowerCase(),u=o;if("m"===i&&c.length>2&&(r.push(t([u],c.splice(0,2),!0)),i="l",u="m"===u?"l":"L"),c.length<e[i])return"";for(r.push(t([u],c.splice(0,e[i]),!0));c.length>=e[i]&&c.length&&e[i];)r.push(t([u],c.splice(0,e[i]),!0));return""})),r}function r(t,e){var n=t.x*Math.cos(e)-t.y*Math.sin(e),a=t.y*Math.cos(e)+t.x*Math.sin(e);t.x=n,t.y=a}function s(t,e){t.x*=e,t.y*=e}var c=function(){function t(e){var n;this.commands=[],e&&e instanceof t?(n=this.commands).push.apply(n,e.commands):e&&(this.commands=o(e))}return t.prototype.addPath=function(e){var n;e&&e instanceof t&&(n=this.commands).push.apply(n,e.commands)},t.prototype.moveTo=function(t,e){this.commands.push(["M",t,e])},t.prototype.lineTo=function(t,e){this.commands.push(["L",t,e])},t.prototype.arc=function(t,e,n,a,o,r){this.commands.push(["AC",t,e,n,a,o,!!r])},t.prototype.arcTo=function(t,e,n,a,o){this.commands.push(["AT",t,e,n,a,o])},t.prototype.ellipse=function(t,e,n,a,o,r,s,c){this.commands.push(["E",t,e,n,a,o,r,s,!!c])},t.prototype.closePath=function(){this.commands.push(["Z"])},t.prototype.bezierCurveTo=function(t,e,n,a,o,r){this.commands.push(["C",t,e,n,a,o,r])},t.prototype.quadraticCurveTo=function(t,e,n,a){this.commands.push(["Q",t,e,n,a])},t.prototype.rect=function(t,e,n,a){this.commands.push(["R",t,e,n,a])},t.prototype.roundRect=function(t,e,n,a,o){void 0===o?this.commands.push(["RR",t,e,n,a,0]):this.commands.push(["RR",t,e,n,a,o])},t}();function i(t,e){var n,a,o,c,i,u,h,l,p,m,y,d,f,v,x,T,b,g,C,k,R,M,q,w,z=0,P=0,A=null,D=null,E=null,L=null,I=null,Q=null;t.beginPath();for(var S=0;S<e.length;++S){"S"!==(g=e[S][0])&&"s"!==g&&"C"!==g&&"c"!==g&&(A=null,D=null),"T"!==g&&"t"!==g&&"Q"!==g&&"q"!==g&&(E=null,L=null);var F=void 0;switch(g){case"m":case"M":F=e[S],"m"===g?(z+=F[1],P+=F[2]):(z=F[1],P=F[2]),"M"!==g&&I||(I={x:z,y:P}),t.moveTo(z,P);break;case"l":z+=(F=e[S])[1],P+=F[2],t.lineTo(z,P);break;case"L":z=(F=e[S])[1],P=F[2],t.lineTo(z,P);break;case"H":z=(F=e[S])[1],t.lineTo(z,P);break;case"h":z+=(F=e[S])[1],t.lineTo(z,P);break;case"V":P=(F=e[S])[1],t.lineTo(z,P);break;case"v":P+=(F=e[S])[1],t.lineTo(z,P);break;case"a":case"A":if(F=e[S],null===Q)throw new Error("This should never happen");"a"===g?(z+=F[6],P+=F[7]):(z=F[6],P=F[7]),v=F[1],x=F[2],h=F[3]*Math.PI/180,o=!!F[4],c=!!F[5],i={x:z,y:P},r(u={x:(Q.x-i.x)/2,y:(Q.y-i.y)/2},-h),(l=u.x*u.x/(v*v)+u.y*u.y/(x*x))>1&&(v*=l=Math.sqrt(l),x*=l),p=v*v*x*x,m=v*v*u.y*u.y+x*x*u.x*u.x,s(C={x:v*u.y/x,y:-x*u.x/v},c!==o?Math.sqrt((p-m)/m)||0:-Math.sqrt((p-m)/m)||0),a=Math.atan2((u.y-C.y)/x,(u.x-C.x)/v),n=Math.atan2(-(u.y+C.y)/x,-(u.x+C.x)/v),r(C,h),M=C,q=(i.x+Q.x)/2,w=(i.y+Q.y)/2,M.x+=q,M.y+=w,t.save(),t.translate(C.x,C.y),t.rotate(h),t.scale(v,x),t.arc(0,0,1,a,n,!c),t.restore();break;case"C":A=(F=e[S])[3],D=F[4],z=F[5],P=F[6],t.bezierCurveTo(F[1],F[2],A,D,z,P);break;case"c":F=e[S],t.bezierCurveTo(F[1]+z,F[2]+P,F[3]+z,F[4]+P,F[5]+z,F[6]+P),A=F[3]+z,D=F[4]+P,z+=F[5],P+=F[6];break;case"S":F=e[S],null!==A&&null!==D||(A=z,D=P),t.bezierCurveTo(2*z-A,2*P-D,F[1],F[2],F[3],F[4]),A=F[1],D=F[2],z=F[3],P=F[4];break;case"s":F=e[S],null!==A&&null!==D||(A=z,D=P),t.bezierCurveTo(2*z-A,2*P-D,F[1]+z,F[2]+P,F[3]+z,F[4]+P),A=F[1]+z,D=F[2]+P,z+=F[3],P+=F[4];break;case"Q":E=(F=e[S])[1],L=F[2],z=F[3],P=F[4],t.quadraticCurveTo(E,L,z,P);break;case"q":E=(F=e[S])[1]+z,L=F[2]+P,z+=F[3],P+=F[4],t.quadraticCurveTo(E,L,z,P);break;case"T":null!==E&&null!==L||(E=z,L=P),E=2*z-E,L=2*P-L,z=(F=e[S])[1],P=F[2],t.quadraticCurveTo(E,L,z,P);break;case"t":null!==E&&null!==L||(E=z,L=P),E=2*z-E,L=2*P-L,z+=(F=e[S])[1],P+=F[2],t.quadraticCurveTo(E,L,z,P);break;case"z":case"Z":I&&(z=I.x,P=I.y),I=null,t.closePath();break;case"AC":z=(F=e[S])[1],P=F[2],f=F[3],a=F[4],n=F[5],k=F[6],t.arc(z,P,f,a,n,k);break;case"AT":y=(F=e[S])[1],d=F[2],z=F[3],P=F[4],f=F[5],t.arcTo(y,d,z,P,f);break;case"E":z=(F=e[S])[1],P=F[2],v=F[3],x=F[4],h=F[5],a=F[6],n=F[7],k=F[8],t.save(),t.translate(z,P),t.rotate(h),t.scale(v,x),t.arc(0,0,1,a,n,k),t.restore();break;case"R":z=(F=e[S])[1],P=F[2],T=F[3],b=F[4],I={x:z,y:P},t.rect(z,P,T,b);break;case"RR":z=(F=e[S])[1],P=F[2],T=F[3],b=F[4],R=F[5],I={x:z,y:P},t.roundRect(z,P,T,b,R)}Q?(Q.x=z,Q.y=P):Q={x:z,y:P}}}function u(t,e,n,a,o){var r=this;if(void 0===o&&(o=0),"number"==typeof o&&(o=[o]),Array.isArray(o)){if(0===o.length||o.length>4)throw new RangeError("Failed to execute 'roundRect' on '".concat(this.constructor.name,"': ").concat(o.length," radii provided. Between one and four radii are necessary."));if(o.forEach((function(t){if(t<0)throw new RangeError("Failed to execute 'roundRect' on '".concat(r.constructor.name,"': Radius value ").concat(t," is negative."))})),1===o.length&&0===o[0])return this.rect(t,e,n,a);var s,c,i,u=Math.min(n,a)/2,h=s=c=i=Math.min(u,o[0]);2===o.length&&(s=i=Math.min(u,o[1])),3===o.length&&(s=i=Math.min(u,o[1]),c=Math.min(u,o[2])),4===o.length&&(s=Math.min(u,o[1]),c=Math.min(u,o[2]),i=Math.min(u,o[3])),this.moveTo(t,e+a-i),this.arcTo(t,e,t+h,e,h),this.arcTo(t+n,e,t+n,e+s,s),this.arcTo(t+n,e+a,t+n-c,e+a,c),this.arcTo(t,e+a,t,e+a-i,i),this.moveTo(t,e)}}!function(t){if(t&&t.CanvasRenderingContext2D&&!t.Path2D){var e=t.CanvasRenderingContext2D,n=e.prototype.fill,a=e.prototype.stroke,o=e.prototype.isPointInPath;e.prototype.fill=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(!(t[0]instanceof c)){o=t[0]||"nonzero";return n.apply(this,[o])}var a=t[0],o=t[1]||"nonzero";i(this,a.commands),n.apply(this,[o])},e.prototype.stroke=function(t){t&&i(this,t.commands),a.apply(this)},e.prototype.isPointInPath=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(t[0]instanceof c){var n=t[0],a=t[1],r=t[2],s=t[3]||"nonzero";return i(this,n.commands),o.apply(this,[a,r,s])}return o.apply(this,t)},t.Path2D=c}}(window),function(t){if(t&&t.CanvasRenderingContext2D){var e=t.CanvasRenderingContext2D,n=t.Path2D;e&&!e.prototype.roundRect&&(e.prototype.roundRect=u),n&&!n.prototype.roundRect&&(n.prototype.roundRect=u)}}(window)}();
