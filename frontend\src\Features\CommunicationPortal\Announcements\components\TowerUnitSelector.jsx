import React, { useState, useEffect } from 'react';
import { Controller } from 'react-hook-form';
import { Building, Home, Users } from 'lucide-react';

/**
 * TowerUnitSelector Component
 * Handles tower and unit selection with checkboxes
 */
const TowerUnitSelector = ({ control, errors, setValue, watch }) => {
  const [towers, setTowers] = useState([]);
  const [units, setUnits] = useState({});
  const [loading, setLoading] = useState(true);

  const selectedTowers = watch('selectedTowers') || [];
  const selectedUnits = watch('selectedUnits') || [];

  // Mock data - replace with actual API calls
  useEffect(() => {
    const fetchTowersAndUnits = async () => {
      try {
        // TODO: Replace with actual API calls
        const mockTowers = [
          { id: '1', name: 'Tower A', description: 'Main residential tower' },
          { id: '2', name: 'Tower B', description: 'Commercial tower' },
          { id: '3', name: 'Tower C', description: 'Mixed-use tower' }
        ];

        const mockUnits = {
          '1': [
            { id: '1-1', name: 'A-101', floor: 1, type: 'Residential' },
            { id: '1-2', name: 'A-102', floor: 1, type: 'Residential' },
            { id: '1-3', name: 'A-201', floor: 2, type: 'Residential' },
            { id: '1-4', name: 'A-202', floor: 2, type: 'Residential' }
          ],
          '2': [
            { id: '2-1', name: 'B-101', floor: 1, type: 'Commercial' },
            { id: '2-2', name: 'B-102', floor: 1, type: 'Commercial' },
            { id: '2-3', name: 'B-201', floor: 2, type: 'Commercial' }
          ],
          '3': [
            { id: '3-1', name: 'C-101', floor: 1, type: 'Mixed' },
            { id: '3-2', name: 'C-102', floor: 1, type: 'Mixed' },
            { id: '3-3', name: 'C-201', floor: 2, type: 'Mixed' },
            { id: '3-4', name: 'C-202', floor: 2, type: 'Mixed' },
            { id: '3-5', name: 'C-301', floor: 3, type: 'Mixed' }
          ]
        };

        setTowers(mockTowers);
        setUnits(mockUnits);
      } catch (error) {
        console.error('Error fetching towers and units:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTowersAndUnits();
  }, []);

  // Handle tower selection
  const handleTowerChange = (towerId, isChecked) => {
    let updatedTowers;
    if (isChecked) {
      updatedTowers = [...selectedTowers, towerId];
    } else {
      updatedTowers = selectedTowers.filter(id => id !== towerId);
      // Also remove all units from this tower
      const towerUnits = units[towerId] || [];
      const towerUnitIds = towerUnits.map(unit => unit.id);
      const updatedUnits = selectedUnits.filter(unitId => !towerUnitIds.includes(unitId));
      setValue('selectedUnits', updatedUnits);
    }
    setValue('selectedTowers', updatedTowers);
  };

  // Handle unit selection
  const handleUnitChange = (unitId, isChecked) => {
    let updatedUnits;
    if (isChecked) {
      updatedUnits = [...selectedUnits, unitId];
    } else {
      updatedUnits = selectedUnits.filter(id => id !== unitId);
    }
    setValue('selectedUnits', updatedUnits);
  };

  // Handle select all units for a tower
  const handleSelectAllUnits = (towerId, isChecked) => {
    const towerUnits = units[towerId] || [];
    const towerUnitIds = towerUnits.map(unit => unit.id);
    
    let updatedUnits;
    if (isChecked) {
      // Add all units from this tower
      updatedUnits = [...new Set([...selectedUnits, ...towerUnitIds])];
    } else {
      // Remove all units from this tower
      updatedUnits = selectedUnits.filter(unitId => !towerUnitIds.includes(unitId));
    }
    setValue('selectedUnits', updatedUnits);
  };

  // Check if all units in a tower are selected
  const areAllUnitsSelected = (towerId) => {
    const towerUnits = units[towerId] || [];
    const towerUnitIds = towerUnits.map(unit => unit.id);
    return towerUnitIds.length > 0 && towerUnitIds.every(unitId => selectedUnits.includes(unitId));
  };

  // Get available units for selected towers
  const getAvailableUnits = () => {
    const availableUnits = [];
    selectedTowers.forEach(towerId => {
      const towerUnits = units[towerId] || [];
      availableUnits.push(...towerUnits.map(unit => ({ ...unit, towerId })));
    });
    return availableUnits;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Loading towers and units...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Tower Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Select Towers <span className="text-[#3D9D9B]">*</span>
        </label>
        <div className="space-y-2 max-h-48 overflow-y-auto border border-gray-200 rounded-md p-3">
          {towers.map((tower) => (
            <label key={tower.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
              <input
                type="checkbox"
                checked={selectedTowers.includes(tower.id)}
                onChange={(e) => handleTowerChange(tower.id, e.target.checked)}
                className="w-4 h-4 text-[#3D9D9B] border-gray-300 rounded focus:ring-[#3D9D9B] accent-[#3D9D9B]"
              />
              <Building className="w-4 h-4 text-[#3D9D9B]" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700">{tower.name}</p>
                <p className="text-xs text-gray-500">{tower.description}</p>
              </div>
            </label>
          ))}
        </div>
        {errors.selectedTowers && (
          <p className="mt-1 text-sm text-red-600">{errors.selectedTowers.message}</p>
        )}
      </div>

      {/* Unit Selection */}
      {selectedTowers.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Select Units <span className="text-[#3D9D9B]">*</span>
          </label>
          <div className="space-y-4">
            {selectedTowers.map((towerId) => {
              const tower = towers.find(t => t.id === towerId);
              const towerUnits = units[towerId] || [];
              
              return (
                <div key={towerId} className="border border-gray-200 rounded-md p-3">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                      <Building className="w-4 h-4 text-[#3D9D9B]" />
                      <span>{tower?.name}</span>
                    </h4>
                    <label className="flex items-center space-x-2 text-sm text-[#3D9D9B] cursor-pointer">
                      <input
                        type="checkbox"
                        checked={areAllUnitsSelected(towerId)}
                        onChange={(e) => handleSelectAllUnits(towerId, e.target.checked)}
                        className="w-4 h-4 text-[#3D9D9B] border-gray-300 rounded focus:ring-[#3D9D9B] accent-[#3D9D9B]"
                      />
                      <span>Select All</span>
                    </label>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto">
                    {towerUnits.map((unit) => (
                      <label key={unit.id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedUnits.includes(unit.id)}
                          onChange={(e) => handleUnitChange(unit.id, e.target.checked)}
                          className="w-4 h-4 text-[#3D9D9B] border-gray-300 rounded focus:ring-[#3D9D9B] accent-[#3D9D9B]"
                        />
                        <Home className="w-3 h-3 text-gray-400" />
                        <span className="text-sm text-gray-700">{unit.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
          {errors.selectedUnits && (
            <p className="mt-1 text-sm text-red-600">{errors.selectedUnits.message}</p>
          )}
        </div>
      )}

      {/* Selection Summary */}
      {(selectedTowers.length > 0 || selectedUnits.length > 0) && (
        <div className="p-4 bg-[#3D9D9B]/5 rounded-md border border-[#3D9D9B]/20">
          <div className="flex items-center space-x-2 mb-2">
            <Users className="w-4 h-4 text-[#3D9D9B]" />
            <span className="text-sm font-medium text-[#3D9D9B]">Audience Summary</span>
          </div>
          <p className="text-sm text-gray-600">
            Selected {selectedTowers.length} tower(s) and {selectedUnits.length} unit(s)
          </p>
          <p className="text-xs text-gray-500 mt-1">
            This announcement will be sent to residents/owners/staff of the selected units.
          </p>
        </div>
      )}
    </div>
  );
};

export default TowerUnitSelector;
