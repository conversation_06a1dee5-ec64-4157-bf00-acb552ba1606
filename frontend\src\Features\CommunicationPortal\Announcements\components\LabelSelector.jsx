import React, { useState, useEffect } from 'react';
import { Plus, Tag, ChevronDown, X } from 'lucide-react';

/**
 * LabelSelector Component
 * Handles creating new labels with text input and selecting from existing ones
 */
const LabelSelector = ({ value, onChange, error }) => {
  const [newLabelName, setNewLabelName] = useState('');
  const [existingLabels, setExistingLabels] = useState([]);
  const [isCreatingLabel, setIsCreatingLabel] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showCreateInput, setShowCreateInput] = useState(false);

  // Load existing labels on component mount
  useEffect(() => {
    loadExistingLabels();
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isDropdownOpen && !event.target.closest('.label-selector-dropdown')) {
        setIsDropdownOpen(false);
        setShowCreateInput(false);
        setNewLabelName('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isDropdownOpen]);

  // Load labels from localStorage
  const loadExistingLabels = () => {
    try {
      const savedLabels = JSON.parse(localStorage.getItem('announcementLabels') || '[]');
      setExistingLabels(savedLabels);
    } catch (error) {
      console.error('Error loading labels:', error);
      setExistingLabels([]);
    }
  };

  // Save labels to localStorage
  const saveLabelsToStorage = (labels) => {
    try {
      localStorage.setItem('announcementLabels', JSON.stringify(labels));
    } catch (error) {
      console.error('Error saving labels:', error);
    }
  };

  // Handle selecting existing label
  const handleSelectLabel = (labelName) => {
    const currentLabels = value ? value.split(',').map(l => l.trim()) : [];
    let newLabels;
    
    if (currentLabels.includes(labelName)) {
      // Remove label if already selected
      newLabels = currentLabels.filter(l => l !== labelName);
    } else {
      // Add new label
      newLabels = [...currentLabels, labelName];
    }
    
    onChange(newLabels.join(', '));
  };

  // Handle creating new label
  const handleCreateLabel = async () => {
    if (!newLabelName.trim()) return;
  
    // Check if label already exists (case-insensitive)
    const labelExists = existingLabels.some(
      label => label.name.toLowerCase() === newLabelName.trim().toLowerCase()
    );
  
    if (labelExists) {
      alert('Label already exists!');
      return;
    }
  
    setIsCreatingLabel(true);
    try {
      const newLabel = {
        id: `label-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        name: newLabelName.trim(),
        color: `#${Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')}`
      };
      
      const updatedLabels = [...existingLabels, newLabel];
      setExistingLabels(updatedLabels);
      saveLabelsToStorage(updatedLabels);
      
      // Add new label to current selection
      const currentLabels = value ? value.split(',').map(l => l.trim()) : [];
      const newLabels = [...currentLabels, newLabel.name];
      onChange(newLabels.join(', '));
      
      setNewLabelName('');
      setShowCreateInput(false);
      setIsDropdownOpen(false);
    } catch (error) {
      console.error('Error creating label:', error);
    } finally {
      setIsCreatingLabel(false);
    }
  };

  // Handle key press for creating label
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleCreateLabel();
    }
  };

  // Check if a label is selected
  const isLabelSelected = (labelName) => {
    if (!value) return false;
    return value.split(',').map(l => l.trim()).includes(labelName);
  };

  return (
    <div>
      <label className="block text-sm font-semibold text-gray-700 mb-2">
        Labels <span className="text-[#3D9D9B]">*</span>
      </label>

      {/* Main Label Input/Dropdown */}
      <div className="relative label-selector-dropdown">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-transparent cursor-pointer bg-white flex items-center justify-between"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          <div className="flex items-center flex-wrap gap-2 min-h-[24px]">
            {value ? (
              value.split(',').map((labelName, index) => (
                <span
                  key={index}
                  className="inline-flex items-center bg-[#3D9D9B] text-white text-sm px-3 py-1 rounded-full"
                >
                  {labelName.trim()}
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      const currentLabels = value.split(',').map(l => l.trim());
                      const newLabels = currentLabels.filter((_, i) => i !== index);
                      onChange(newLabels.join(', '));
                    }}
                    className="ml-2 text-white hover:text-gray-200 focus:outline-none"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))
            ) : (
              <span className="text-gray-500">Select labels...</span>
            )}
          </div>
          <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform flex-shrink-0 ml-2 ${isDropdownOpen ? 'rotate-180' : ''}`} />
        </div>

        {/* Dropdown Menu */}
        {isDropdownOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
            {/* Create New Label Input */}
            {showCreateInput ? (
              <div className="p-3 border-b border-gray-200">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newLabelName}
                    onChange={(e) => setNewLabelName(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Enter new label name..."
                    className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-[#3D9D9B]"
                    maxLength={50}
                    autoFocus
                  />
                  <button
                    type="button"
                    onClick={handleCreateLabel}
                    disabled={!newLabelName.trim() || isCreatingLabel}
                    className="px-2 py-1 bg-[#3D9D9B] text-white rounded text-sm hover:bg-[#34877A] disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isCreatingLabel ? '...' : 'Add'}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowCreateInput(false);
                      setNewLabelName('');
                    }}
                    className="px-2 py-1 text-gray-500 hover:text-gray-700"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ) : (
              <div className="p-2 border-b border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowCreateInput(true)}
                  className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-[#3D9D9B] rounded"
                >
                  <Plus className="w-4 h-4" />
                  <span>Create New Label</span>
                </button>
              </div>
            )}

            {/* Existing Labels */}
            <div className="py-1">
              {existingLabels.length > 0 ? (
                existingLabels.map((label, index) => (
                  <div key={label.id}>
                    <button
                      type="button"
                      onClick={() => handleSelectLabel(label.name)}
                      className={`w-full text-left px-3 py-2 text-sm flex items-center space-x-2 ${
                        isLabelSelected(label.name) ? 'bg-[#3D9D9B] text-white' : 'text-gray-900'
                      }`}
                    >
                      <Tag className="w-4 h-4" style={{ color: isLabelSelected(label.name) ? 'white' : label.color }} />
                      <span>{label.name}</span>
                    </button>
                    {index < existingLabels.length - 1 && <div className="h-2" />}
                  </div>
                ))
              ) : (
                <div className="p-3 text-sm text-gray-500 text-center">
                  No labels created yet
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default LabelSelector;
