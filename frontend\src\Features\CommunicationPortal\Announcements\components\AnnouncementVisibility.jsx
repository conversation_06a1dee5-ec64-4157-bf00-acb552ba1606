import React from 'react';
import { Controller } from 'react-hook-form';
import DatePicker from 'react-datepicker';
import { Calendar, Clock } from 'lucide-react';
import 'react-datepicker/dist/react-datepicker.css';

/**
 * AnnouncementVisibility Component
 * Handles start/end date and time selection with status classification
 */
const AnnouncementVisibility = ({ control, errors, setValue, watch }) => {
  const startDate = watch('startDate');
  const startTime = watch('startTime');
  const endDate = watch('endDate');
  const endTime = watch('endTime');

  // Generate time options (24-hour format)
  const generateTimeOptions = () => {
    const times = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        times.push(timeString);
      }
    }
    return times;
  };

  const timeOptions = generateTimeOptions();

  // Get announcement status based on dates and times
  const getAnnouncementStatus = () => {
    if (!startDate || !startTime || !endDate || !endTime) {
      return { status: 'draft', color: '#6B7280', bgColor: '#F9FAFB' };
    }

    const now = new Date();
    const startDateTime = new Date(`${startDate.toDateString()} ${startTime}`);
    const endDateTime = new Date(`${endDate.toDateString()} ${endTime}`);

    if (now < startDateTime) {
      return { status: 'Upcoming', color: '#F59E0B', bgColor: '#FFFBEB' };
    } else if (now >= startDateTime && now <= endDateTime) {
      return { status: 'On-Going', color: '#10B981', bgColor: '#F0FDF4' };
    } else {
      return { status: 'Expired', color: '#EF4444', bgColor: '#FEF2F2' };
    }
  };

  const statusInfo = getAnnouncementStatus();

  // Custom time input component
  const TimeInput = ({ value, onChange, placeholder, error }) => (
    <div className="relative">
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-[#3D9D9B] appearance-none bg-white"
      >
        <option value="">{placeholder}</option>
        {timeOptions.map((time) => (
          <option key={time} value={time}>
            {time}
          </option>
        ))}
      </select>
      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <Clock className="w-4 h-4 text-[#3D9D9B]" />
      </div>
      {error && (
        <p className="mt-1 text-sm text-red-600">{error.message}</p>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Start Date and Time */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Start Date <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="startDate"
            control={control}
            render={({ field: { onChange, value } }) => (
              <div className="relative">
                <DatePicker
                  selected={value}
                  onChange={onChange}
                  dateFormat="dd-MM-yyyy"
                  placeholderText="Select start date"
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-[#3D9D9B]"
                  minDate={new Date()}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <Calendar className="w-4 h-4 text-[#3D9D9B]" />
                </div>
              </div>
            )}
          />
          {errors.startDate && (
            <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Start Time <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="startTime"
            control={control}
            render={({ field: { onChange, value } }) => (
              <TimeInput
                value={value}
                onChange={onChange}
                placeholder="Select start time"
                error={errors.startTime}
              />
            )}
          />
        </div>
      </div>

      {/* End Date and Time */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            End Date <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="endDate"
            control={control}
            render={({ field: { onChange, value } }) => (
              <div className="relative">
                <DatePicker
                  selected={value}
                  onChange={onChange}
                  dateFormat="dd-MM-yyyy"
                  placeholderText="Select end date"
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#3D9D9B] focus:border-[#3D9D9B]"
                  minDate={startDate || new Date()}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <Calendar className="w-4 h-4 text-[#3D9D9B]" />
                </div>
              </div>
            )}
          />
          {errors.endDate && (
            <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            End Time <span className="text-[#3D9D9B]">*</span>
          </label>
          <Controller
            name="endTime"
            control={control}
            render={({ field: { onChange, value } }) => (
              <TimeInput
                value={value}
                onChange={onChange}
                placeholder="Select end time"
                error={errors.endTime}
              />
            )}
          />
        </div>
      </div>

      {/* Status Preview */}
      {statusInfo.status !== 'draft' && (
        <div className="p-4 rounded-md border" style={{ backgroundColor: statusInfo.bgColor }}>
          <div className="flex items-center space-x-2">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: statusInfo.color }}
            />
            <span 
              className="text-sm font-medium"
              style={{ color: statusInfo.color }}
            >
              Status: {statusInfo.status}
            </span>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            {statusInfo.status === 'Upcoming' && 'This announcement will be active in the future.'}
            {statusInfo.status === 'On-Going' && 'This announcement is currently active.'}
            {statusInfo.status === 'Expired' && 'This announcement has ended.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default AnnouncementVisibility;
