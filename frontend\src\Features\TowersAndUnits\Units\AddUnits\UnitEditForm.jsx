import React from "react";
import { FaAddressBook } from "react-icons/fa";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import MultipleImageDropzoneUnitEdit from "../components/MultipleImageDropzoneUnitEdit";

const UnitEditForm = ({
  selectedUnitDetails,
  onSubmit,
  handleUpload,
  contactType,
  setContactType,
  showModal,
  setShowModal,
  memberContact,
  handleContactSelect,
  uploadedImages,
  register,
  handleSubmit,
  setValue,
  errors,
  isDirty
}) => {
  const phoneValidation = {
    validate: (value) =>
      !value ||
      /^(018|019|013|017|015|016|014)\d{8}$/.test(value) ||
      "Invalid Bangladeshi phone number"
  };

  const emailValidation = {
    validate: (value) =>
      !value ||
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(value) ||
      "Enter a valid email address"
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="p-[20px]" method="POST">
      <h3 className="text-base text-primary font-bold pb-4">
        General Information
      </h3>
      <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
        <div className="login-field">
          <input
            type="number"
            {...register("area", {
              required: "Area is required",
              min: { value: 1, message: "Area must be greater than 0" }
            })}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Area"
          />
          <label className="block login-field-label text-sm font-medium">
            Area/ sq. ft.
          </label>
          {errors.area && <ErrorMessage message={errors.area.message} />}
        </div>

        <div className="login-field">
          <input
            type="number"
            {...register("number_of_rooms", {
              required: "Number of rooms is required",
              min: {
                value: 1,
                message: "Number of rooms must be greater than 0"
              }
            })}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Number of Rooms"
          />
          <label className="block login-field-label text-sm font-medium">
            Number of Rooms
          </label>
          {errors.number_of_rooms && (
            <ErrorMessage message={errors.number_of_rooms.message} />
          )}
        </div>

        <div className="login-field">
          <input
            type="number"
            {...register("number_of_bathrooms", {
              required: "Number of bathrooms is required",
              min: {
                value: 0,
                message: "Number of bathrooms must be at least 0"
              }
            })}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Number of Bathrooms"
          />
          <label className="block login-field-label text-sm font-medium">
            Number of Bathrooms
          </label>
          {errors.number_of_bathrooms && (
            <ErrorMessage message={errors.number_of_bathrooms.message} />
          )}
        </div>

        <div className="login-field">
          <input
            type="number"
            {...register("number_of_balconies", {
              required: "Number of balconies is required",
              min: {
                value: 0,
                message: "Number of balconies must be at least 0"
              }
            })}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Number of Balconies"
          />
          <label className="block login-field-label text-sm font-medium">
            Number of Balconies
          </label>
          {errors.number_of_balconies && (
            <ErrorMessage message={errors.number_of_balconies.message} />
          )}
        </div>
      </div>

      {/* Primary Contact */}
      <div className="flex justify-between items-center">
        <h3 className="text-base text-primary font-bold pb-4 mt-4">
          Primary Contact
        </h3>
        <button
          type="button"
          onClick={() => {
            setContactType("primary");
            setShowModal(true);
          }}
          className="bg-primary text-white rounded text-sm py-2 px-4 m-2 flex items-center"
        >
          <FaAddressBook className="mr-2" />
          Primary Contact
        </button>
      </div>
      <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
        <div className="login-field">
          <input
            type="text"
            {...register("primary_name")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Name"
          />
          <label className="block login-field-label text-sm font-medium">
            Name
          </label>
        </div>
        <div className="login-field">
          <input
            type="text"
            {...register("primary_number", phoneValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Phone Number"
          />
          <label className="block login-field-label text-sm font-medium">
            Phone Number
          </label>
          {errors.primary_number && (
            <ErrorMessage message={errors.primary_number.message} />
          )}
        </div>

        <div className="login-field">
          <input
            type="email"
            {...register("primary_email", emailValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Email"
          />
          <label className="block login-field-label text-sm font-medium">
            Email
          </label>
          {errors.primary_email && (
            <ErrorMessage message={errors.primary_email.message} />
          )}
        </div>
        <div className="login-field">
          <input
            type="text"
            {...register("primary_relationship")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Relationship"
          />
          <label className="block login-field-label text-sm font-medium">
            Relationship
          </label>
        </div>
      </div>

      {/* Secondary Contact */}
      <div className="flex justify-between items-center">
        <h3 className="text-base text-primary font-bold pb-4 mt-4">
          Secondary Contact
        </h3>
        <button
          type="button"
          onClick={() => {
            setContactType("secondary");
            setShowModal(true);
          }}
          className="bg-primary text-white rounded text-sm py-2 px-4 m-2 flex items-center"
        >
          <FaAddressBook className="mr-2" />
          Secondary Contact
        </button>
      </div>
      <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
        <div className="login-field">
          <input
            type="text"
            {...register("secondary_name")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Name"
          />
          <label className="block login-field-label text-sm font-medium">
            Name
          </label>
        </div>
        <div className="login-field">
          <input
            type="text"
            {...register("secondary_number", phoneValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Phone Number"
          />
          <label className="block login-field-label text-sm font-medium">
            Phone Number
          </label>
          {errors.secondary_number && (
            <ErrorMessage message={errors.secondary_number.message} />
          )}
        </div>

        <div className="login-field">
          <input
            type="email"
            {...register("secondary_email", emailValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Email"
          />
          <label className="block login-field-label text-sm font-medium">
            Email
          </label>
          {errors.secondary_email && (
            <ErrorMessage message={errors.secondary_email.message} />
          )}
        </div>
        <div className="login-field">
          <input
            type="text"
            {...register("secondary_relationship")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Relationship"
          />
          <label className="block login-field-label text-sm font-medium">
            Relationship
          </label>
        </div>
      </div>

      {/* Emergency Contact */}
      <h3 className="text-base text-primary font-bold pb-4 mt-4">
        Emergency Contact
      </h3>
      <div className="grid grid-cols-2 gap-3 lg:w-[687px]">
        <div className="login-field">
          <input
            type="text"
            {...register("emergency_name")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Name"
          />
          <label className="block login-field-label text-sm font-medium">
            Name
          </label>
        </div>
        <div className="login-field">
          <input
            type="text"
            {...register("emergency_number", phoneValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Phone Number"
          />
          <label className="block login-field-label text-sm font-medium">
            Phone Number
          </label>
          {errors.emergency_number && (
            <ErrorMessage message={errors.emergency_number.message} />
          )}
        </div>

        <div className="login-field">
          <input
            type="email"
            {...register("emergency_email", emailValidation)}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Emergency Email"
          />
          <label className="block login-field-label text-sm font-medium">
            Email
          </label>
          {errors.emergency_email && (
            <ErrorMessage message={errors.emergency_email.message} />
          )}
        </div>
        <div className="login-field">
          <input
            type="text"
            {...register("emergency_relationship")}
            className="border rounded p-2 w-full login-field-input"
            placeholder="Relationship"
          />
          <label className="block login-field-label text-sm font-medium">
            Relationship
          </label>
        </div>

        <div className="flex justify-left gap-2 pb-5 col-span-2">
          <MultipleImageDropzoneUnitEdit
            onUpload={handleUpload}
            initialFiles={selectedUnitDetails?.docs || []}
          />
        </div>
      </div>

      <div className="lg:w-[687px] mt-4">
      <button
          type="submit"
          className={`px-4 py-2 mt-3 text-white font-semibold rounded transition-all duration-200 w-full ${
            isDirty
              ? "bg-primary text-white"
              : "bg-gray-400 text-white cursor-not-allowed"
          }`}
          disabled={!isDirty}
        >
          Update Unit
        </button>
      </div>
    </form>
  );
};

export default UnitEditForm;
