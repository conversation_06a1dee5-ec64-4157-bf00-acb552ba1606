import { useEffect, useState, useRef, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Paragraph } from "../../../../Components/Ui/Paragraph";
import { Heading } from "../../../../Components/Ui/Heading";
import { GoPlus, GoTrash } from "react-icons/go";
import ArrowHeading from "../../../../Components/HeadingComponent/ArrowHeading";
import TextInputComponent from "../../../../Components/FormComponent/TextInputComponent";
import NumberInputComponent from "../../../../Components/FormComponent/NumberInputComponent";
import CheckboxComponent from "../../../../Components/FormComponent/CheckboxComponent";
import RadioComponent from "../../../../Components/FormComponent/RadioComponent";
import DynamicTowerTable from "../components/DynamicTowerTable";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import SubmitButton from "../../../../Components/FormComponent/ButtonComponent/SubmitButton";
import { checkPermission } from "../../../../utils/permissionUtils";

import {
  createTower,
  clearMessages,
  getLastTowerNumber
} from "../../../../redux/slices/towers/towerSlice";

const schema = yup.object().shape({
  tower_name: yup
    .string()
    .max(50, "Tower name cannot exceed 50 characters.")
    .required("Tower name is required."),

  num_floors: yup
    .number()
    .typeError("Number of floors must be a number")
    .required("Number of floors is required.")
    .min(1, "Number of floors must be greater than 0") // Ensures positive number
    .max(50, "Number of floors cannot exceed 100"), // Ensures maximum of 50

  num_units: yup
    .number()
    .typeError("Units per floor must be a number")
    .required("Units per floor are required.")
    .min(1, "Units per floor must be greater than 0") // Ensures positive number
    .max(26, "Units per floor cannot exceed 26"), // Ensures maximum of 26

  unit_naming_type: yup.string().required("Unit naming is required."),
  add_tower_number_to_unit_name: yup.boolean(),
  units_per_floor: yup.string().required("Unit type is required."),

  // number_of_units: yup.array().of(
  //     yup.number()
  //         .typeError("Number of units must be a number")
  //         .min(1, "Units must be greater than 0")
  // ).required("Unit distribution is required when using 'Different' configuration"),

  number_of_units: yup.array().when("units_per_floor", {
    is: "Different",
    then: (schema) =>
      schema
        .of(
          yup
            .number()
            .typeError("Number of units must be a number")
            .required("Each unit field must be filled.")
            .min(1, "Units must be greater than 0")
        )
        .min(
          1,
          "At least one unit must be specified when 'Different' is selected."
        )
  }),

  photo: yup
    .mixed()
    .test("fileSize", "File size must be less than 5MB", (value) => {
      if (!value || value.length === 0) return true; // Allow empty file
      return value.size <= 5 * 1024 * 1024;
    })
    .test("fileType", "Unsupported file format", (value) => {
      if (!value || value.length === 0) return true;
      return ["image/jpeg", "image/png"].includes(value.type);
    }),

  description: yup
    .string()
    .transform((value, originalValue) => {
      return originalValue.trim() === "" ? undefined : value;
    })
    .test("wordCount", "Description cannot exceed 100 words", (value) => {
      if (!value) return true;
      const wordCount = value.trim().split(/\s+/).length;
      return wordCount <= 100;
    })
});
const AddTower = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { error, successMessage, lastTowerNumber } = useSelector(
    (state) => state.tower
  );

  // State Hooks
  const [imagePreview, setImagePreview] = useState(null);
  const [fileError, setFileError] = useState("");
  const [floorEdits, setFloorEdits] = useState([]);
  const [unitsPerFloor, setUnitsPerFloor] = useState("Same as Every Floor");

  const [hasPermission, setHasPermission] = useState(false);
  const [loadingPermission, setLoadingPermission] = useState(true);
  const [floorEditErrors, setFloorEditErrors] = useState([]);

  useEffect(() => {
    const fetchPermission = async () => {
      const permissionGranted = await checkPermission("org", 10);
      setHasPermission(permissionGranted);
      setLoadingPermission(false);
    };
    fetchPermission();
  }, []);

  // React Hook Form
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      tower_name: "",
      description: "",
      num_floors: "",
      num_units: "",
      num_floors_edit: "",
      num_units_edit: "",
      unit_naming_type: "Numerical",
      add_tower_number_to_unit_name: false,
      units_per_floor: "Same as Every Floor"
    }
  });

  // Watch Inputs
  const numFloors = watch("num_floors") || 0;
  const numUnits = watch("num_units") || 0;

  useEffect(() => {
    // console.log("Validation Errors:", errors);
  }, [errors]);

  // Fetch last tower number on component mount
  useEffect(() => {
    dispatch(getLastTowerNumber());
  }, [dispatch]);

  // Auto-set tower_number (No user input allowed)
  useEffect(() => {
    if (lastTowerNumber !== null) {
      // console.log("Fetched lastTowerNumber:", lastTowerNumber);
      setValue("tower_number", lastTowerNumber);
    }
  }, [lastTowerNumber, setValue]);

  useEffect(() => {
    if (successMessage || error) {
      return; // Do nothing, let the user handle message clearing
    }
  }, [successMessage, error]);

  // Handle File Upload
  const handleFileChange = useCallback(
    (event) => {
      const file = event.target.files[0];

      if (!file) return;

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setFileError("File size must be less than 5MB");
        return;
      }

      // Validate file type (JPEG, PNG)
      if (!["image/jpeg", "image/png"].includes(file.type)) {
        setFileError(
          "Unsupported file format. Only JPG, JPEG, and PNG are allowed."
        );
        return;
      }

      // Clear previous errors
      setFileError("");
      clearErrors("photo");

      // Read file and set preview
      const reader = new FileReader();
      reader.onloadend = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
      setValue("photo", file);
    },
    [setValue, clearErrors]
  );

  const fileInputRef = useRef(null);

  const handleRemoveImage = () => {
    setImagePreview(null);
    setValue("photo", null);
    setFileError("");
    clearErrors("photo");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  useEffect(() => {
    if (unitsPerFloor === "Same as Every Floor") {
      // Reset floorEdits since we no longer need per-floor modifications
      setFloorEdits([]);

      // Ensure all floors have the same unit count
      const updatedUnits = new Array(numFloors).fill(numUnits);
      setValue("number_of_units", updatedUnits);
    }
  }, [unitsPerFloor, numFloors, numUnits, setValue]);

  useEffect(() => {
    if (numFloors > 0 && numUnits > 0) {
      let updatedUnits = new Array(numFloors).fill(numUnits);

      floorEdits.forEach((edit) => {
        if (edit.floor >= 1 && edit.floor <= numFloors) {
          updatedUnits[edit.floor - 1] = edit.units || numUnits;
        }
      });

      // console.log("Final number_of_units:", updatedUnits);
      setValue("number_of_units", updatedUnits);
    }
  }, [numFloors, numUnits, floorEdits, setValue]);

  // **Handle adding a new floor edit**
  // const addFloorEdit = () => {
  //     setFloorEdits((prevEdits) => {
  //         // Prevent duplicate floor entries
  //         const floorNumbers = prevEdits.map((edit) => edit.floor);

  //         if (floorNumbers.includes("")) {
  //             return prevEdits; // Prevent adding multiple empty floors
  //         }

  //         return [...prevEdits, { floor: "", units: "" }];
  //     });
  // };

  const [maxFloorReached, setMaxFloorReached] = useState(false);

  const addFloorEdit = () => {
    if (floorEdits.length >= numFloors) {
      setMaxFloorReached(true);
      return; // Prevent adding more floors
    }
    setMaxFloorReached(false);

    setFloorEdits((prevEdits) => [...prevEdits, { floor: "", units: "" }]);
  };

  const updateFloorEdit = (index, field, value) => {
    setFloorEdits((prevEdits) => {
      const updatedEdits = [...prevEdits];
      let updatedErrors = [...floorEditErrors];

      if (field === "floor") {
        if (value === "" || isNaN(value)) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            floor: "Floor number is required."
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits; // Prevent updating with empty value
        }

        if (value < 1 || value > numFloors) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            floor: `Floor number must be between 1 and ${numFloors}.`
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits;
        }

        // Check if floor already exists
        const floorExists = updatedEdits.some(
          (edit, i) => edit.floor === value && i !== index
        );

        if (floorExists) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            floor:
              "This floor is already assigned. Please choose another floor."
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits;
        } else {
          updatedErrors[index] = { ...updatedErrors[index], floor: "" }; // Clear error if valid
        }

        // Ensure that if a floor is set, units must also be set
        if (!updatedEdits[index].units || isNaN(updatedEdits[index].units)) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            units: "Units per floor is required."
          };
          setFloorEditErrors(updatedErrors);
        }
      }

      if (field === "units") {
        if (value === "" || isNaN(value)) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            units: "Units per floor is required."
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits;
        }

        if (value < 1 || value > 26) {
          updatedErrors[index] = {
            ...updatedErrors[index],
            units: "Units must be between 1 and 26."
          };
          setFloorEditErrors(updatedErrors);
          return prevEdits;
        } else {
          updatedErrors[index] = { ...updatedErrors[index], units: "" }; // Clear error if valid
        }
      }

      setFloorEditErrors(updatedErrors);
      updatedEdits[index][field] = value;
      return updatedEdits;
    });

    let updatedUnits = new Array(numFloors).fill(numUnits);
    floorEdits.forEach((edit) => {
      if (edit.floor >= 1 && edit.floor <= numFloors) {
        updatedUnits[edit.floor - 1] = edit.units || numUnits;
      }
    });

    setValue("number_of_units", updatedUnits);
  };

  // **Handle removing an edited floor**
  // const removeFloorEdit = (index) => {
  //   const updatedEdits = floorEdits.filter((_, i) => i !== index);
  //   setFloorEdits(updatedEdits);

  //   let updatedUnits = new Array(numFloors).fill(numUnits);
  //   updatedEdits.forEach((edit) => {
  //     if (edit.floor >= 1 && edit.floor <= numFloors) {
  //       updatedUnits[edit.floor - 1] = edit.units || numUnits;
  //     }
  //   });

  //   setValue("number_of_units", updatedUnits);
  // };

  const removeFloorEdit = (index) => {
    const updatedEdits = floorEdits.filter((_, i) => i !== index);
    setFloorEdits(updatedEdits);

    let updatedUnits = new Array(numFloors).fill(numUnits);
    updatedEdits.forEach((edit) => {
      if (edit.floor >= 1 && edit.floor <= numFloors) {
        updatedUnits[edit.floor - 1] = edit.units || numUnits;
      }
    });

    setValue("number_of_units", updatedUnits);

    setFloorEditErrors((prevErrors) => {
      const newErrors = [...prevErrors];
      newErrors.splice(index, 1);
      return newErrors;
    });
  };

  const hasErrors = floorEditErrors.some((err) => err?.floor || err?.units);
  // const allFloorEditsValid = floorEdits.every(
  //   (edit) => edit.floor && edit.units
  // );
  // const allFieldsFilled =
  //   watch("tower_name") &&
  //   watch("num_floors") &&
  //   watch("num_units") &&
  //   watch("unit_naming_type") &&
  //   (unitsPerFloor === "Same as Every Floor" || allFloorEditsValid);
  // const isSaveDisabled =
  //   hasErrors || !allFieldsFilled || Object.keys(errors).length > 0;

  // start
  // update code .......................................... 05-21-25
  const allFloorEditsValid = floorEdits.every(
    (edit) =>
      edit.floor &&
      edit.units &&
      !isNaN(edit.floor) &&
      !isNaN(edit.units) &&
      edit.floor >= 1 &&
      edit.floor <= numFloors &&
      edit.units >= 1 &&
      edit.units <= 26
  );

  const requiredFieldsFilled =
    watch("tower_name") &&
    watch("num_floors") > 0 &&
    watch("num_units") > 0 &&
    watch("unit_naming_type");

  const isDifferentConfigValid =
    unitsPerFloor !== "Different" ||
    (unitsPerFloor === "Different" &&
      floorEdits.length > 0 &&
      allFloorEditsValid &&
      floorEdits.length === new Set(floorEdits.map((edit) => edit.floor)).size); // Ensure no duplicate floors

  const hasFormErrors = Object.keys(errors).length > 0;

  const isSaveDisabled =
    !requiredFieldsFilled ||
    !isDifferentConfigValid ||
    hasErrors ||
    hasFormErrors;
  // end
  // update code........................................  05-21-25

  const onSubmit = (data) => {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (Array.isArray(value))
        value.forEach((val) => formData.append(key, val));
      else formData.append(key, value);
    });

    dispatch(createTower(formData));
  };

  // const onSubmit = async (data) => {
  //     const formData = new FormData();

  //     for (const [key, value] of Object.entries(data)) {
  //         if (Array.isArray(value)) {
  //             value.forEach((val) => formData.append(key, val));
  //         } else {
  //             formData.append(key, value);
  //         }
  //     }

  //     dispatch(createTower(formData));
  // };

  if (loadingPermission) {
    return <div></div>;
  }

  if (!hasPermission) {
    navigate("/not-authorized");
  }

  return (
    <div className="h-full">
      <div className="container">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="bg-white pt-2 mx-auto px-5">
            <div className="md:flex justify-between py-2">
              <ArrowHeading
                title={"Add Tower"}
                size="xl"
                color="black"
                onNext={() => navigate(-1)}
                fontWeight="semibold"
              />
            </div>
            <div className="md:flex md:gap-6">
              <div className="w-[60%] px-5">
                <Heading level={1} className="my-2 text-sm font-medium">
                  Tower & Unit Information
                </Heading>
                <div className="flex gap-4">
                  <div className="w-1/2">
                    <Paragraph className="my-3 text-sm font-medium">
                      Tower Name*
                    </Paragraph>
                    <TextInputComponent
                      {...register("tower_name")}
                      placeholder="Enter Tower Name"
                      value={watch("tower_name") || ""}
                      onChange={(e) => {
                        setValue("tower_name", e.target.value); // Ensure state updates
                        clearErrors("tower_name"); // ✅ Clears validation error when valid input is entered
                      }}
                      error={errors.tower_name?.message}
                    />
                  </div>
                  <div className="w-1/2">
                    <Paragraph className="my-3 text-sm font-medium">
                      Tower Number
                    </Paragraph>
                    <NumberInputComponent
                      value={watch("tower_number") || lastTowerNumber || ""}
                      disabled // Ensure it's disabled
                    />
                  </div>
                </div>
                <div className="w-full mt-4">
                  <Paragraph className="my-3 text-sm font-medium">
                    Description
                  </Paragraph>
                  <div className="login-field">
                    <textarea
                      className="login-field-input"
                      {...register("description")}
                      onChange={(e) => {
                        clearErrors("description");
                      }}
                      rows="4" // Optional: height of the textarea
                      cols="50" // Optional: width of the textarea
                    />
                    <div>
                      {errors?.description && (
                        <p className="text-error text-[12px]">
                          {errors.description.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
                <Paragraph className="my-3 text-sm font-medium">
                  Photo
                </Paragraph>
                <div className="relative w-1/2 pt-5 pb-5 rounded-27 profile-picture flex flex-col items-center border border-dashed">
                  <label
                    htmlFor="file-upload"
                    className="relative cursor-pointer w-24 h-24 flex items-center justify-center overflow-hidden"
                  >
                    {imagePreview ? (
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="object-cover w-full h-full rounded-md"
                      />
                    ) : (
                      <img
                        src="./image_upload.png"
                        alt="Upload Placeholder"
                        className="m-3 w-[45px] h-[45px]"
                      />
                    )}
                  </label>
                  <input
                    id="file-upload"
                    type="file"
                    accept=".jpg,.jpeg,.png"
                    className="hidden"
                    onChange={handleFileChange}
                    ref={fileInputRef}
                  />
                  {imagePreview && (
                    <button
                      type="button"
                      onClick={handleRemoveImage}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center shadow-lg hover:bg-red-600"
                      aria-label="Remove Image"
                    >
                      &#10006;
                    </button>
                  )}
                  {!imagePreview && <p>Upload Tower Photo</p>}
                  {fileError && (
                    <p className="text-red-500 text-sm">{fileError}</p>
                  )}
                  {errors?.photo && (
                    <p className="text-red-500 text-sm">
                      {errors.photo.message}
                    </p>
                  )}
                </div>
                <div className="flex gap-4 mt-4">
                  <div className="w-1/2">
                    <Paragraph className="my-3 text-sm font-medium">
                      Number of Floors
                    </Paragraph>
                    <NumberInputComponent
                      {...register("num_floors")}
                      type="number"
                      min="1"
                      max="50" // Restricts user input
                      value={numFloors}
                      onChange={(e) => {
                        let value = parseInt(e.target.value) || 1;
                        value = Math.min(50, Math.max(1, value)); // ✅ Prevents over 100
                        setValue("num_floors", value);
                        clearErrors("num_floors"); // ✅ Clears validation error when valid input is entered
                      }}
                      error={errors.num_floors?.message}
                    />
                  </div>
                  <div className="w-1/2">
                    <Paragraph className="my-3 text-sm font-medium">
                      Units in Each Floor
                    </Paragraph>
                    <NumberInputComponent
                      {...register("num_units")}
                      type="number"
                      min="1"
                      max="26" // Restricts user input
                      value={numUnits}
                      onChange={(e) => {
                        let value = parseInt(e.target.value) || 1;
                        value = Math.min(26, Math.max(1, value)); // ✅ Prevents over 26
                        setValue("num_units", value);
                        clearErrors("num_units"); // ✅ Clears validation error when valid input is entered
                      }}
                      error={errors.num_units?.message}
                    />
                  </div>
                </div>
                <div className="flex item-center justify-between gap-2 mt-2">
                  <div className="w-1/2">
                    <Paragraph className="my-3 text-sm font-medium">
                      Unit Naming*
                    </Paragraph>
                    <RadioComponent
                      options={[
                        { label: "Numerical", value: "Numerical" },
                        { label: "Alphabetical", value: "Alphabetical" }
                      ]}
                      selectedValue={watch("unit_naming_type")}
                      onChange={(e) =>
                        setValue("unit_naming_type", e.target.value)
                      }
                      name="unit_naming_type"
                    />
                  </div>
                  <div className="w-1/2 mt-12">
                    <CheckboxComponent
                      label="Add Tower Number to Unit Name"
                      checked={watch("add_tower_number_to_unit_name")}
                      onChange={(e) =>
                        setValue(
                          "add_tower_number_to_unit_name",
                          e.target.checked
                        )
                      }
                      name="add_tower_number_to_unit_name"
                    />
                  </div>
                </div>
              </div>
              <div className="w-[40%] px-5 md:border-l-[#F9F9FB] md:border-l-[4px]">
                <div className="w-full pl-5">
                  <Paragraph className="my-3 text-sm font-medium">
                    Unit*
                  </Paragraph>
                  
                  <RadioComponent
                    options={[
                      {
                        label: "Same as Every Floor",
                        value: "Same as Every Floor"
                      },
                      { label: "Different", value: "Different" }
                    ]}
                    selectedValue={unitsPerFloor}
                    onChange={(e) => {
                      const value = e.target.value;
                      setUnitsPerFloor(value);
                      setValue("units_per_floor", value);

                      if (value === "Same as Every Floor") {
                        setFloorEdits([]);
                        setFloorEditErrors([]);
                        setValue(
                          "number_of_units",
                          new Array(numFloors).fill(numUnits)
                        );
                        setMaxFloorReached(false);
                      }
                    }}
                    name="units_per_floor"
                  />
                </div>
                {unitsPerFloor === "Different" && (
                  <>
                    <div className="flex flex-col gap-4 mt-4 pl-5">
                      <button
                        type="button"
                        onClick={addFloorEdit}
                        className="flex justify-center items-center w-10 h-10 rounded-full bg-[#ECF5F5] hover:bg-gray-300 transition-all"
                        disabled={floorEdits.length >= numFloors} // Disable if all floors are assigned
                        aria-label="Add Floor Unit"
                      >
                        <GoPlus className="text-black text-xl" />
                      </button>
                      {maxFloorReached && (
                        <p className="text-red-500 text-sm">
                          Maximum floor limit reached
                        </p>
                      )}

                      {floorEdits.map((edit, index) => (
                        <div key={index} className="flex flex-col gap-2">
                          <div className="flex items-center gap-4 mb-2">
                            {/* Floor Number Input */}
                            <div>
                              <NumberInputComponent
                                name={`floor_edit_${index}`}
                                placeholder="Floor Number"
                                value={edit.floor}
                                onChange={(e) => {
                                  let value =
                                    e.target.value.trim() === ""
                                      ? ""
                                      : Number(e.target.value);
                                  updateFloorEdit(index, "floor", value);
                                }}
                              />
                              {floorEditErrors[index]?.floor && (
                                <p className="text-red-500 text-sm">
                                  {floorEditErrors[index].floor}
                                </p>
                              )}
                            </div>

                            {/* Units Input */}
                            <div>
                              <NumberInputComponent
                                name={`units_edit_${index}`}
                                placeholder="Units on Floor"
                                value={edit.units}
                                onChange={(e) => {
                                  let value =
                                    e.target.value.trim() === ""
                                      ? ""
                                      : Number(e.target.value);
                                  updateFloorEdit(index, "units", value);
                                }}
                              />
                              {floorEditErrors[index]?.units && (
                                <p className="text-red-500 text-sm">
                                  {floorEditErrors[index].units}
                                </p>
                              )}
                            </div>

                            {/* Remove Button */}
                            <button
                              type="button"
                              onClick={() => removeFloorEdit(index)}
                              className="flex justify-center items-center w-10 h-10 rounded-full bg-[#ECF5F5] hover:bg-gray-300 transition-all"
                              aria-label="Remove Floor Unit"
                            >
                              <GoTrash className="text-black text-xl" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                )}
                <div>
                  <DynamicTowerTable
                    num_floors={watch("num_floors") || 0}
                    num_units={watch("num_units") || 0}
                    num_floors_edit={watch("num_floors_edit") || 0}
                    num_units_edit={watch("num_units_edit") || 0}
                    unit_naming_type={watch("unit_naming_type") || "Numerical"}
                    add_tower_number_to_unit_name={
                      watch("add_tower_number_to_unit_name") || false
                    }
                    tower_number={watch("tower_number") || 1}
                    units_per_floor={
                      watch("units_per_floor") || "Same as Every Floor"
                    }
                    floorEdits={floorEdits}
                  />
                </div>
              </div>
            </div>
            <div className="w-[50%] mx-auto mt-10 pb-10">
              <SubmitButton
                text="Save"
                width="full"
                disabled={isSaveDisabled}
              />
            </div>
          </div>
        </form>
      </div>
      <MessageBox
        message={successMessage}
        error={error}
        clearMessage={() => dispatch(clearMessages())}
        onOk={() => {
          dispatch(clearMessages());
          if (successMessage) navigate(-1);
        }}
      />
    </div>
  );
};
export default AddTower;
