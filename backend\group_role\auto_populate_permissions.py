import os
import sys
import django
from django.db import connection

# Add the root directory of your project to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

django.setup()

# Now you can import models and run your population logic
from group_role.models import Permission  # Import your model

class AutoPopulatePermissions:
    def populate_permissions(self):
        required_permissions = [
            "Create Member", "Edit Member", "View Member List",
            "Create Role", "Edit Role", "View Role List",
            "Create Group", "Edit Group", "View Group List", 
            "Create Tower", "Edit Tower", "View Tower"
        ]

        # Check for existing permissions
        existing_permissions = set(Permission.objects.values_list('permission_name', flat=True))
        missing_permissions = set(required_permissions) - existing_permissions

        if missing_permissions:
            # If there are missing permissions, truncate the table and re-create
            self.truncate_permissions_table()  # Call function to truncate the table
            Permission.objects.bulk_create([
                Permission(permission_name=permission_name)
                for permission_name in required_permissions
            ])
            print(f"Permissions populated: {required_permissions}")
        else:
            print("All required permissions are already in the table.")
    def truncate_permissions_table(self):
        # Get the table name for the Permission model
        table_name = Permission._meta.db_table
        print(table_name)

        # Get the database connection and execute raw SQL to truncate the table
        with connection.cursor() as cursor:
            cursor.execute("SET foreign_key_checks = 0;")
            cursor.execute(f"TRUNCATE TABLE {table_name};")  # Truncate the table
            cursor.execute(f"ALTER TABLE {table_name} AUTO_INCREMENT = 1;")  # Reset the auto-increment
            cursor.execute("SET foreign_key_checks = 1;")
