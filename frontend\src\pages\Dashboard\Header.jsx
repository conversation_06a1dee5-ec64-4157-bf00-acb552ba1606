import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { setAuthFromLocalStorage } from "../../redux/slices/authSlice/authSlice";
import { fetchHeadingData } from "../../redux/slices/api/memberApi";
import user from "../../assets/user/user.png";
import axios from "axios";

const api = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
});

const UserProfile = ({ member }) => {
  const navigate = useNavigate();
  const imageUrl = api.defaults.baseURL + member?.photo_low_quality;

  return (
    <div
      className="flex items-center space-x-2 cursor-pointer"
      onClick={() => navigate(`/member-profile/${member.id}`)}
    >
      <img
        src={imageUrl}
        onError={(e) => { e.target.src = user; }}
        className="w-10 h-10 border border-[#3D9D9B] rounded-full object-cover"
        alt="User Avatar"
        loading="lazy"
      />
      <div>
        <h2 className="text-[#3D9D9B] text-lg">{member.full_name}</h2>
        <p className="text-[#3D9D9B] text-sm">{member.roles.join(", ")}</p>
      </div>
    </div>
  );
};

const Header = ({ toggleSidebar }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isAuthenticated } = useSelector((state) => state.auth);
  // Subscribe to headingData from the Redux member slice
  const member = useSelector((state) => state.member.headingData);

  useEffect(() => {
    dispatch(setAuthFromLocalStorage());
  }, [dispatch]);

  // Fetch heading data on mount if authenticated
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchHeadingData());
    }
  }, [dispatch, isAuthenticated]);

  return (
    <header className="bg-white border-b-2 border-gray-300 px-6 py-5 shadow-md flex justify-between items-center">
      {/* Left Section: Sidebar Toggle & Logo */}
      <div className="flex items-center">
        <button
          className="lg:hidden px-4 focus:outline-none"
          onClick={toggleSidebar}
          aria-label="Toggle Sidebar"
          aria-expanded="false"
          aria-controls="sidebar-menu"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 40 40"
            strokeWidth={2}
            stroke="currentColor"
            className="w-9 h-9 text-[#3D9D9B]"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M4 6h16M4 12h16m-7 6h7"
            />
          </svg>
        </button>
        <img
          src="/estate_link.png"
          alt="Estate Link Logo"
          className="w-[120px] h-auto"
          loading="lazy"
        />
      </div>

      <div className="flex items-center space-x-3">
        {/* Search Icon */}
        <button
          className="w-10 h-10 border border-[#3D9D9B] rounded flex justify-center items-center"
          aria-label="Search"
        >
          <img
            src="/search-head.png"
            className="w-6"
            alt="Search Icon"
            loading="lazy"
          />
        </button>

        {/* Notifications Icon */}
        <button
          className="w-10 h-10 border border-[#3D9D9B] rounded flex justify-center items-center"
          aria-label="Notifications"
        >
          <img
            src="/bell.png"
            className="w-6"
            alt="Notification Bell"
            loading="lazy"
          />
        </button>

        {isAuthenticated ? (
          <UserProfile member={member} />
        ) : (
          <button
            onClick={() => navigate("/login")}
            className="text-[#3D9D9B] border border-[#3D9D9B] px-4 py-1 rounded"
          >
            Login
          </button>
        )}
      </div>
    </header>
  );
};

export default Header;
